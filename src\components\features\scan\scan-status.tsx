import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { type ScanStatusType } from '@/components/ui/status-badge'
import { Button } from '@/components/ui/button'
import { Alert } from '@/components/ui/alert'
import { ScanTerminal } from './scan-terminal'
import { type ScanLog } from '@/hooks/use-scan-events'
import {
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  Copy,
  ExternalLink,
  Square
} from 'lucide-react'

interface ScanStatusProps {
  scanId: string
  status: ScanStatusType
  message: string
  onViewResults?: () => void
  onCancel?: () => Promise<void>
  className?: string
  // Real-time data
  logs?: ScanLog[]
  isConnected?: boolean
  showTerminal?: boolean
}

const getStatusConfig = (status: ScanStatusType) => {
  switch (status) {
    case 'PENDING':
      return {
        icon: Clock,
        title: 'Scan Queued',
        description: 'Your scan is in the queue and will start shortly',
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200',
        progressColor: 'bg-yellow-500'
      }
    case 'RUNNING':
      return {
        icon: Loader2,
        title: 'Scanning in Progress',
        description: 'Analyzing your website for vulnerabilities',
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
        progressColor: 'bg-blue-500',
        animate: true
      }
    case 'COMPLETED':
      return {
        icon: CheckCircle,
        title: 'Scan Complete',
        description: 'Your security scan has finished successfully',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        progressColor: 'bg-green-500'
      }
    case 'FAILED':
      return {
        icon: XCircle,
        title: 'Scan Failed',
        description: 'Something went wrong during the scan',
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        progressColor: 'bg-red-500'
      }
    case 'CANCELLED':
      return {
        icon: AlertTriangle,
        title: 'Scan Cancelled',
        description: 'The scan was cancelled before completion',
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
        progressColor: 'bg-gray-500'
      }
    default:
      return {
        icon: Clock,
        title: 'Unknown Status',
        description: '',
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
        progressColor: 'bg-gray-500'
      }
  }
}

export const ScanStatus: React.FC<ScanStatusProps> = ({
  scanId,
  status,
  message,
  onViewResults,
  onCancel,
  className,
  logs = [],
  isConnected = false,
  showTerminal = false,
}) => {
  const [isCancelling, setIsCancelling] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const config = getStatusConfig(status)
  const Icon = config.icon

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(scanId)
    } catch (err) {
      console.error('Failed to copy scan ID:', err)
    }
  }

  const handleCancel = async () => {
    if (!onCancel) return

    if (!confirm('Are you sure you want to cancel this scan? This action cannot be undone.')) {
      return
    }

    try {
      setIsCancelling(true)
      setError(null)
      await onCancel()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to cancel scan')
    } finally {
      setIsCancelling(false)
    }
  }

  return (
    <Card className={`${className} shadow-xl border-0 bg-white overflow-hidden`}>
      <CardContent className="p-0">
        {/* Status Header */}
        <div className={`${config.bgColor} ${config.borderColor} border-b-2 p-6`}>
          <div className="flex items-center space-x-4">
            <div className={`w-12 h-12 rounded-full bg-white flex items-center justify-center shadow-sm`}>
              <Icon
                className={`h-6 w-6 ${config.color} ${config.animate ? 'animate-spin' : ''}`}
              />
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-bold text-gray-900 mb-1">
                {config.title}
              </h3>
              <p className="text-gray-600">
                {config.description}
              </p>
            </div>
          </div>

          {/* Progress Bar for Running Status */}
          {status === 'RUNNING' && (
            <div className="mt-6">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Scanning...</span>
                <span>Please wait</span>
              </div>
              <div className="w-full bg-white rounded-full h-2 shadow-inner">
                <div className="bg-blue-500 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Scan ID */}
          <div className="bg-gray-50 rounded-xl p-4">
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-gray-700">Scan ID</label>
              <button
                onClick={copyToClipboard}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                title="Copy to clipboard"
              >
                <Copy className="h-4 w-4" />
              </button>
            </div>
            <code className="text-sm font-mono text-gray-900 bg-white px-3 py-2 rounded-lg border block">
              {scanId}
            </code>
          </div>

          {/* Message */}
          {message && (
            <div className="bg-blue-50 rounded-xl p-4 border border-blue-200">
              <div className="flex items-start space-x-3">
                <div className="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <p className="text-sm text-blue-800 font-medium">{message}</p>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="mt-4">
              <Alert variant="error" className="border-0 shadow-lg bg-red-50/80 backdrop-blur-sm">
                {error}
              </Alert>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-2">
            {status === 'COMPLETED' && onViewResults && (
              <Button
                onClick={onViewResults}
                className="flex-1 h-12 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Eye className="h-5 w-5 mr-2" />
                View Results
              </Button>
            )}

            {(status === 'RUNNING' || status === 'PENDING') && onCancel && (
              <Button
                onClick={handleCancel}
                disabled={isCancelling}
                variant="destructive"
                className="flex-1 h-12 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
              >
                {isCancelling ? (
                  <>
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                    Cancelling...
                  </>
                ) : (
                  <>
                    <Square className="h-5 w-5 mr-2" />
                    Cancel Scan
                  </>
                )}
              </Button>
            )}

            {status === 'FAILED' && (
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="flex-1 h-12 border-2 border-gray-200 hover:border-gray-300 text-gray-700 font-semibold rounded-xl transition-all duration-200"
              >
                Try Again
              </Button>
            )}

            {(status === 'COMPLETED' || status === 'FAILED') && (
              <Button
                onClick={() => window.location.href = '/dashboard/scans'}
                variant="outline"
                className="px-6 h-12 border-2 border-gray-200 hover:border-gray-300 text-gray-700 font-medium rounded-xl transition-all duration-200"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                All Scans
              </Button>
            )}
          </div>

          {/* Assessment Console */}
          {showTerminal && (
            <div className="mt-6">
              <div className="mb-3 text-sm text-gray-600 bg-gray-50 rounded-lg p-3 border">
                <div className="flex items-center justify-between">
                  <span className="font-semibold text-gray-800">
                    🔍 <strong>Real-time Assessment Progress</strong>
                  </span>
                  <span className="text-xs">
                    Events: {logs.length} | Status: {isConnected ? '🟢 Active' : '🔴 Inactive'}
                  </span>
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  Monitor your security assessment in real-time as our platform analyzes your assets
                </p>
              </div>
              <ScanTerminal
                logs={logs}
                isConnected={isConnected}
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
