import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { handleApiError } from '@/lib/errors'
import { jobQueue } from '@/lib/job-queue'

export async function GET(request: NextRequest) {
  try {
    // Authentication
    await requireAuth()

    // Get queue status
    const status = await jobQueue.getQueueStatus()

    return NextResponse.json({
      queue: status,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return handleApiError(error)
  }
}
