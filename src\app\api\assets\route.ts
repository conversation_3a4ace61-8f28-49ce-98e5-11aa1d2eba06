import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { assetSchema, validateData } from '@/lib/validations'
import { handleApiError, ValidationError, ConflictError } from '@/lib/errors'

export async function GET(request: NextRequest) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50)
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      userId: currentUser.userId
    }

    if (status) {
      where.status = status.toUpperCase()
    }

    if (search) {
      where.OR = [
        { url: { contains: search } },
        { domain: { contains: search } },
        { title: { contains: search } }
      ]
    }

    // Get assets with scan statistics
    const [assets, total] = await Promise.all([
      db.asset.findMany({
        where,
        include: {
          _count: {
            select: {
              scans: true,
              vulnerabilities: true
            }
          },
          scans: {
            select: {
              id: true,
              status: true,
              createdAt: true,
              totalVulns: true,
              criticalVulns: true,
              highVulns: true
            },
            orderBy: {
              createdAt: 'desc'
            },
            take: 1
          }
        },
        orderBy: {
          lastScanned: 'desc'
        },
        skip: offset,
        take: limit
      }),
      db.asset.count({ where })
    ])

    return NextResponse.json({
      assets,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    return handleApiError(error)
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Parse and validate request body
    const body = await request.json()
    const validation = validateData(assetSchema, body)
    
    if (!validation.success) {
      throw new ValidationError('Invalid asset data')
    }

    const { url, title, description } = validation.data

    // Extract domain from URL
    const domain = new URL(url).hostname

    // Check if asset already exists
    const existingAsset = await db.asset.findFirst({
      where: {
        userId: currentUser.userId,
        url: url
      }
    })

    if (existingAsset) {
      throw new ConflictError('Asset with this URL already exists')
    }

    // Create asset
    const asset = await db.asset.create({
      data: {
        url,
        domain,
        title: title || `Asset for ${domain}`,
        description,
        userId: currentUser.userId,
        status: 'ACTIVE'
      },
      include: {
        _count: {
          select: {
            scans: true,
            vulnerabilities: true
          }
        }
      }
    })

    return NextResponse.json(
      {
        message: 'Asset created successfully',
        asset
      },
      { status: 201 }
    )

  } catch (error) {
    return handleApiError(error)
  }
}
