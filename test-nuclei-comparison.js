// Test script to compare web wrapper vs direct Nuclei commands
const { spawn } = require('child_process')
const fs = require('fs').promises
const path = require('path')

const TARGET_URL = 'https://juice-shop.herokuapp.com'

// Templates from our web wrapper configuration
const QUICK_TEMPLATES = [
  'http', 'misconfiguration', 'exposed-panels', 'exposures', 'defaults-logins',
  'miscellaneous', 'ssl', 'technologies', 'takeovers', 'osint', 'dns', 'file',
  'headless', 'javascript', 'dast', 'code', 'workflows', 'network', 'iot',
  'fuzzing', 'cves', 'cnvd', 'token-spray', 'generic-detections'
]

const DEEP_TEMPLATES = [
  ...QUICK_TEMPLATES,
  'auth-bypass', 'backup-files', 'brute-force', 'cms', 'command-injection',
  'cors', 'csrf', 'deserialization', 'directory-traversal', 'file-upload',
  'graphql', 'injection', 'lfi', 'nosql-injection', 'open-redirect',
  'prototype-pollution', 'race-condition', 'rce', 'rfi', 'sqli', 'ssrf',
  'ssti', 'subdomain-takeover', 'traversal', 'unrestricted-file-upload',
  'weak-credentials', 'xss', 'xxe', 'cloud', 'docker', 'kubernetes',
  'aws', 'azure', 'gcp', 'database', 'elasticsearch', 'mongodb',
  'mysql', 'postgresql', 'redis', 'apache', 'nginx', 'iis', 'tomcat',
  'jenkins', 'wordpress', 'drupal', 'joomla', 'api', 'websocket',
  'jwt', 'oauth', 'android', 'ios', 'malware', 'phishing',
  'cryptocurrency', 'blockchain'
]

async function runNucleiCommand(templates, outputFile, testName) {
  console.log(`\n🧪 Running ${testName}...`)
  console.log(`📋 Templates: ${templates.length} categories`)
  console.log(`🎯 Target: ${TARGET_URL}`)
  
  const args = [
    '-u', TARGET_URL,
    '-t', templates.join(','),
    '-jsonl',
    '-output', outputFile,
    '-silent',
    '-no-color',
    '-timeout', '120',
    '-concurrency', '25',
    '-rate-limit', '150',
    '-retries', '3',
    '-max-host-error', '10',
    '-bulk-size', '25',
    '-stats',
    '-severity', 'critical,high,medium,low,info,unknown',
    '-disable-update-check',
    '-no-meta',
    '-fr',
    '-mr', '10',
    '-include-rr',
    '-system-resolvers',
    '-disable-clustering'
  ]

  console.log(`🚀 Command: nuclei ${args.join(' ')}`)
  
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    const child = spawn('nuclei', args, { stdio: 'pipe' })
    
    let stdout = ''
    let stderr = ''
    
    child.stdout.on('data', (data) => {
      const output = data.toString()
      stdout += output
      // Show real-time progress
      if (output.includes('[') && output.includes(']')) {
        process.stdout.write('.')
      }
    })
    
    child.stderr.on('data', (data) => {
      stderr += data.toString()
    })
    
    child.on('close', (code) => {
      const duration = Math.round((Date.now() - startTime) / 1000)
      console.log(`\n⏱️  Duration: ${duration}s`)
      console.log(`📤 Exit code: ${code}`)
      
      if (stderr) {
        console.log(`⚠️  Stderr: ${stderr}`)
      }
      
      resolve({ code, stdout, stderr, duration })
    })
    
    child.on('error', (error) => {
      reject(error)
    })
  })
}

async function analyzeResults(outputFile, testName) {
  try {
    const content = await fs.readFile(outputFile, 'utf-8')
    if (!content.trim()) {
      console.log(`❌ ${testName}: No results found`)
      return { total: 0, severities: {} }
    }
    
    const lines = content.trim().split('\n').filter(line => line.trim())
    const vulnerabilities = []
    const severities = {}
    
    for (const line of lines) {
      try {
        const vuln = JSON.parse(line)
        vulnerabilities.push(vuln)
        
        const severity = vuln.info?.severity || 'unknown'
        severities[severity] = (severities[severity] || 0) + 1
      } catch (e) {
        // Skip invalid JSON lines
      }
    }
    
    console.log(`\n📊 ${testName} Results:`)
    console.log(`   🔢 Total vulnerabilities: ${vulnerabilities.length}`)
    console.log(`   📈 Severity breakdown:`)
    
    const severityOrder = ['critical', 'high', 'medium', 'low', 'info', 'unknown']
    for (const severity of severityOrder) {
      const count = severities[severity] || 0
      if (count > 0) {
        console.log(`      ${severity.toUpperCase()}: ${count}`)
      }
    }
    
    // Show sample vulnerabilities
    if (vulnerabilities.length > 0) {
      console.log(`\n📋 Sample vulnerabilities:`)
      vulnerabilities.slice(0, 5).forEach((vuln, index) => {
        const name = vuln.info?.name || 'Unknown'
        const severity = vuln.info?.severity || 'unknown'
        const templateId = vuln['template-id'] || 'unknown'
        console.log(`   ${index + 1}. [${severity.toUpperCase()}] ${name} (${templateId})`)
      })
    }
    
    return { total: vulnerabilities.length, severities, vulnerabilities }
    
  } catch (error) {
    console.log(`❌ Error reading ${testName} results: ${error.message}`)
    return { total: 0, severities: {} }
  }
}

async function main() {
  console.log('🔍 NUCLEI COMPARISON TEST')
  console.log('=' .repeat(60))
  console.log('Comparing web wrapper configuration vs direct Nuclei commands')
  console.log(`Target: ${TARGET_URL}`)
  
  // Ensure temp directory exists
  const tempDir = path.join(process.cwd(), 'temp', 'nuclei')
  await fs.mkdir(tempDir, { recursive: true })
  
  try {
    // Test 1: Quick scan (basic mode)
    const quickOutputFile = path.join(tempDir, 'quick-scan-test.json')
    await runNucleiCommand(QUICK_TEMPLATES, quickOutputFile, 'Quick Scan (Basic Mode)')
    const quickResults = await analyzeResults(quickOutputFile, 'Quick Scan')
    
    // Test 2: Deep scan (advanced mode)
    const deepOutputFile = path.join(tempDir, 'deep-scan-test.json')
    await runNucleiCommand(DEEP_TEMPLATES, deepOutputFile, 'Deep Scan (Advanced Mode)')
    const deepResults = await analyzeResults(deepOutputFile, 'Deep Scan')
    
    // Test 3: Simple comprehensive scan for comparison
    const simpleOutputFile = path.join(tempDir, 'simple-scan-test.json')
    const simpleTemplates = ['cves', 'vulnerabilities', 'exposures', 'misconfiguration', 'default-logins']
    await runNucleiCommand(simpleTemplates, simpleOutputFile, 'Simple Comprehensive Scan')
    const simpleResults = await analyzeResults(simpleOutputFile, 'Simple Scan')
    
    // Comparison summary
    console.log('\n' + '=' .repeat(60))
    console.log('📊 COMPARISON SUMMARY')
    console.log('=' .repeat(60))
    console.log(`Quick Scan:  ${quickResults.total} vulnerabilities`)
    console.log(`Deep Scan:   ${deepResults.total} vulnerabilities`)
    console.log(`Simple Scan: ${simpleResults.total} vulnerabilities`)
    
    console.log('\n🔍 Analysis:')
    if (deepResults.total <= quickResults.total) {
      console.log('❌ ISSUE: Deep scan should find MORE vulnerabilities than quick scan!')
    } else {
      console.log('✅ Deep scan found more vulnerabilities than quick scan')
    }
    
    if (quickResults.total === 0 && deepResults.total === 0) {
      console.log('❌ CRITICAL: No vulnerabilities found in either scan - check template configuration!')
    }
    
    // Check severity distribution
    const hasHighSeverity = (results) => {
      return (results.severities.critical || 0) + (results.severities.high || 0) + (results.severities.medium || 0) > 0
    }
    
    if (!hasHighSeverity(deepResults)) {
      console.log('❌ ISSUE: Deep scan found no critical/high/medium vulnerabilities on Juice Shop!')
      console.log('   This suggests template configuration or filtering issues.')
    }
    
    console.log('\n💡 Recommendations:')
    console.log('1. Compare these results with your web wrapper output')
    console.log('2. Check if web wrapper is using the same templates')
    console.log('3. Verify severity filtering is not excluding results')
    console.log('4. Ensure all template categories are valid and available')
    
    // Clean up
    try {
      await fs.unlink(quickOutputFile)
      await fs.unlink(deepOutputFile)
      await fs.unlink(simpleOutputFile)
    } catch (e) {
      // Ignore cleanup errors
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    process.exit(1)
  }
}

// Run the test
main().catch(error => {
  console.error('Test suite failed:', error)
  process.exit(1)
})
