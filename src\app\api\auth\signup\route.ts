import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { hashPassword, createToken, setAuthCookie } from '@/lib/auth'
import { signupSchema, validateData } from '@/lib/validations'
import { authRateLimit } from '@/lib/rate-limit'
import { handleApiError, ConflictError, RateLimitError } from '@/lib/errors'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = authRateLimit.check(request)
    if (!rateLimitResult.success) {
      throw new RateLimitError('Too many signup attempts. Please try again later.')
    }

    // Parse request body
    const body = await request.json()

    // Validate input data
    const validation = validateData(signupSchema, body)
    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validation.errors
        },
        { status: 400 }
      )
    }

    const { firstName, lastName, companyName, country, email, password } = validation.data

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email: email.toLowerCase() }
    })

    if (existingUser) {
      throw new ConflictError('A user with this email already exists')
    }

    // Hash password
    const hashedPassword = await hashPassword(password)

    // Create user
    const user = await db.user.create({
      data: {
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        companyName: companyName.trim(),
        country: country.trim(),
        email: email.toLowerCase().trim(),
        password: hashedPassword,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        companyName: true,
        country: true,
        email: true,
        createdAt: true,
      }
    })

    // Create JWT token
    const token = await createToken({
      userId: user.id,
      email: user.email,
    })

    // Set auth cookie
    await setAuthCookie(token)

    // Return success response
    return NextResponse.json(
      {
        message: 'Account created successfully',
        user,
      },
      {
        status: 201,
        headers: {
          'X-Rate-Limit-Remaining': rateLimitResult.remaining.toString(),
        }
      }
    )

  } catch (error) {
    return handleApiError(error)
  }
}
