import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { verifyPassword, createToken, setAuthCookie } from '@/lib/auth'
import { loginSchema, validateData } from '@/lib/validations'
import { authRateLimit } from '@/lib/rate-limit'
import { handleApiError, AuthenticationError, RateLimitError } from '@/lib/errors'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = authRateLimit.check(request)
    if (!rateLimitResult.success) {
      throw new RateLimitError('Too many login attempts. Please try again later.')
    }

    // Parse request body
    const body = await request.json()

    // Validate input data
    const validation = validateData(loginSchema, body)
    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validation.errors
        },
        { status: 400 }
      )
    }

    const { email, password } = validation.data

    // Find user by email
    const user = await db.user.findUnique({
      where: { email: email.toLowerCase() }
    })

    if (!user) {
      throw new AuthenticationError('Invalid email or password')
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.password)
    if (!isPasswordValid) {
      throw new AuthenticationError('Invalid email or password')
    }

    // Create JWT token
    const token = await createToken({
      userId: user.id,
      email: user.email,
    })

    // Set auth cookie
    await setAuthCookie(token)

    // Return user data (without password)
    const userData = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      companyName: user.companyName,
      country: user.country,
      email: user.email,
      createdAt: user.createdAt,
    }

    return NextResponse.json(
      {
        message: 'Login successful',
        user: userData,
      },
      {
        status: 200,
        headers: {
          'X-Rate-Limit-Remaining': rateLimitResult.remaining.toString(),
        }
      }
    )

  } catch (error) {
    return handleApiError(error)
  }
}
