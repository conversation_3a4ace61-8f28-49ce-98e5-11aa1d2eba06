import React from 'react'
import { cn } from '@/lib/utils'
import { AlertTriangle, CheckCircle, Info, XCircle } from 'lucide-react'

interface AlertProps {
  variant?: 'success' | 'error' | 'warning' | 'info'
  title?: string
  children: React.ReactNode
  className?: string
  showIcon?: boolean
}

const alertConfig = {
  success: {
    className: 'bg-green-50 border-green-200 text-green-800',
    iconClassName: 'text-green-600',
    icon: CheckCircle,
  },
  error: {
    className: 'bg-red-50 border-red-200 text-red-800',
    iconClassName: 'text-red-600',
    icon: XCircle,
  },
  warning: {
    className: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    iconClassName: 'text-yellow-600',
    icon: AlertTriangle,
  },
  info: {
    className: 'bg-blue-50 border-blue-200 text-blue-800',
    iconClassName: 'text-blue-600',
    icon: Info,
  },
}

export const Alert: React.FC<AlertProps> = ({
  variant = 'info',
  title,
  children,
  className,
  showIcon = true,
}) => {
  const config = alertConfig[variant]
  const Icon = config.icon

  return (
    <div
      className={cn(
        'rounded-lg border p-4',
        config.className,
        className
      )}
    >
      <div className="flex">
        {showIcon && (
          <div className="flex-shrink-0">
            <Icon className={cn('h-5 w-5', config.iconClassName)} />
          </div>
        )}
        <div className={cn('ml-3', !showIcon && 'ml-0')}>
          {title && (
            <h3 className="text-sm font-medium mb-1">{title}</h3>
          )}
          <div className="text-sm">{children}</div>
        </div>
      </div>
    </div>
  )
}
