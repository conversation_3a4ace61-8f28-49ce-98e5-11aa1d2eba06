import React from 'react'
import { cn } from '@/lib/utils'
import { CheckCircle, Clock, XCircle, AlertTriangle, Activity } from 'lucide-react'

export type StatusType = 'success' | 'pending' | 'error' | 'warning' | 'running' | 'info'
export type SeverityType = 'critical' | 'high' | 'medium' | 'low' | 'info' | 'unknown'
export type ScanStatusType = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'

interface StatusBadgeProps {
  status: StatusType
  children: React.ReactNode
  showIcon?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

interface SeverityBadgeProps {
  severity: SeverityType
  children?: React.ReactNode
  showIcon?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

interface ScanStatusBadgeProps {
  status: ScanStatusType
  children?: React.ReactNode
  showIcon?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const statusConfig = {
  success: {
    className: 'bg-green-100 text-green-800 border-green-200',
    icon: CheckCircle,
  },
  pending: {
    className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: Clock,
  },
  error: {
    className: 'bg-red-100 text-red-800 border-red-200',
    icon: XCircle,
  },
  warning: {
    className: 'bg-orange-100 text-orange-800 border-orange-200',
    icon: AlertTriangle,
  },
  running: {
    className: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: Activity,
  },
  info: {
    className: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: AlertTriangle,
  },
}

const severityConfig = {
  critical: {
    className: 'bg-red-100 text-red-800 border-red-200',
    label: 'Critical',
  },
  high: {
    className: 'bg-orange-100 text-orange-800 border-orange-200',
    label: 'High',
  },
  medium: {
    className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    label: 'Medium',
  },
  low: {
    className: 'bg-blue-100 text-blue-800 border-blue-200',
    label: 'Low',
  },
  info: {
    className: 'bg-green-100 text-green-800 border-green-200',
    label: 'Info',
  },
  unknown: {
    className: 'bg-gray-100 text-gray-800 border-gray-200',
    label: 'Unknown',
  },
}

const scanStatusConfig = {
  PENDING: {
    className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: Clock,
    label: 'Pending',
  },
  RUNNING: {
    className: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: Activity,
    label: 'Running',
  },
  COMPLETED: {
    className: 'bg-green-100 text-green-800 border-green-200',
    icon: CheckCircle,
    label: 'Completed',
  },
  FAILED: {
    className: 'bg-red-100 text-red-800 border-red-200',
    icon: XCircle,
    label: 'Failed',
  },
  CANCELLED: {
    className: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: XCircle,
    label: 'Cancelled',
  },
}

const sizeConfig = {
  sm: 'px-2 py-1 text-xs',
  md: 'px-2.5 py-0.5 text-sm',
  lg: 'px-3 py-1 text-base',
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  children,
  showIcon = true,
  size = 'md',
  className,
}) => {
  const config = statusConfig[status]
  const Icon = config.icon

  return (
    <span
      className={cn(
        'inline-flex items-center font-medium rounded-full border',
        config.className,
        sizeConfig[size],
        className
      )}
    >
      {showIcon && <Icon className="w-3 h-3 mr-1" />}
      {children}
    </span>
  )
}

export const SeverityBadge: React.FC<SeverityBadgeProps> = ({
  severity,
  children,
  showIcon = false,
  size = 'md',
  className,
}) => {
  const config = severityConfig[severity]

  return (
    <span
      className={cn(
        'inline-flex items-center font-medium rounded-full border',
        config.className,
        sizeConfig[size],
        className
      )}
    >
      {children || config.label}
    </span>
  )
}

export const ScanStatusBadge: React.FC<ScanStatusBadgeProps> = ({
  status,
  children,
  showIcon = true,
  size = 'md',
  className,
}) => {
  const config = scanStatusConfig[status]
  const Icon = config.icon

  return (
    <span
      className={cn(
        'inline-flex items-center font-medium rounded-full border',
        config.className,
        sizeConfig[size],
        className
      )}
    >
      {showIcon && <Icon className="w-3 h-3 mr-1" />}
      {children || config.label}
    </span>
  )
}
