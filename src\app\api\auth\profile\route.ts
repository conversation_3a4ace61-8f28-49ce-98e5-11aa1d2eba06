import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { updateProfileSchema, validateData } from '@/lib/validations'
import { handleApiError, NotFoundError } from '@/lib/errors'

export async function PUT(request: NextRequest) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Parse request body
    const body = await request.json()

    // Validate input data
    const validation = validateData(updateProfileSchema, body)
    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validation.errors
        },
        { status: 400 }
      )
    }

    const { firstName, lastName, companyName, country } = validation.data

    // Check if user exists
    const existingUser = await db.user.findUnique({
      where: { id: currentUser.userId }
    })

    if (!existingUser) {
      throw new NotFoundError('User not found')
    }

    // Update user profile
    const updatedUser = await db.user.update({
      where: { id: currentUser.userId },
      data: {
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        companyName: companyName.trim(),
        country: country.trim(),
        updatedAt: new Date(),
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        companyName: true,
        country: true,
        email: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    return NextResponse.json(
      {
        message: 'Profile updated successfully',
        user: updatedUser,
      },
      { status: 200 }
    )

  } catch (error) {
    return handleApiError(error)
  }
}

export async function GET() {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Fetch user data from database
    const user = await db.user.findUnique({
      where: { id: currentUser.userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        companyName: true,
        country: true,
        email: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    if (!user) {
      throw new NotFoundError('User not found')
    }

    return NextResponse.json(
      { user },
      { status: 200 }
    )

  } catch (error) {
    return handleApiError(error)
  }
}
