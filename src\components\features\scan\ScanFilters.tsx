import { Badge, Button, Input } from '@/components/ui'
import { Filter, Search, RefreshCw } from 'lucide-react'

export function ScanFilters({
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  onRefresh,
  loading
}: {
  searchTerm: string
  setSearchTerm: (v: string) => void
  statusFilter: string
  setStatusFilter: (v: string) => void
  onRefresh: () => void
  loading: boolean
}) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Search Input */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Search</label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search scans..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className="pl-10 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500"
          />
        </div>
      </div>
      {/* Status Filter */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Status</label>
        <select
          value={statusFilter}
          onChange={e => setStatusFilter(e.target.value)}
          className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
        >
          <option value="">All Statuses</option>
          <option value="PENDING">Pending</option>
          <option value="RUNNING">Running</option>
          <option value="COMPLETED">Completed</option>
          <option value="FAILED">Failed</option>
          <option value="CANCELLED">Cancelled</option>
        </select>
      </div>
      {/* Quick Filters */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Quick Filter</label>
        <div className="flex flex-wrap gap-2">
          <Badge variant="outline" className="cursor-pointer hover:bg-blue-50 text-xs" onClick={() => setStatusFilter('RUNNING')}>Active Scans</Badge>
          <Badge variant="outline" className="cursor-pointer hover:bg-green-50 text-xs" onClick={() => setStatusFilter('COMPLETED')}>Completed</Badge>
          <Badge variant="outline" className="cursor-pointer hover:bg-red-50 text-xs" onClick={() => setStatusFilter('FAILED')}>Failed</Badge>
        </div>
      </div>
      {/* Actions */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Actions</label>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={onRefresh} disabled={loading} className="flex-1">
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
          <Button variant="outline" size="sm" onClick={() => { setSearchTerm(''); setStatusFilter('') }} className="flex-1">
            Clear
          </Button>
        </div>
      </div>
    </div>
  )
}
