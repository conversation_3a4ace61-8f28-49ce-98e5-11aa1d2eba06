/**
 * Basic tests for validation schemas
 * Note: These are simple tests. In a production environment,
 * you would want more comprehensive testing with Jest or similar.
 */

import { signupSchema, loginSchema, validateData } from '../validations'

// Mock test function
function test(name: string, fn: () => void) {
  console.log(`Testing: ${name}`)
  try {
    fn()
    console.log('✅ PASS')
  } catch (err) {
    console.log('❌ FAIL:', err instanceof Error ? err.message : 'Unknown error')
  }
}

// Signup validation tests
test('Valid signup data should pass validation', () => {
  const validData = {
    firstName: 'John',
    lastName: 'Doe',
    companyName: 'Acme Corp',
    country: 'United States',
    email: '<EMAIL>',
    password: 'SecurePass123!',
    confirmPassword: 'SecurePass123!'
  }
  
  const result = validateData(signupSchema, validData)
  if (!result.success) {
    throw new Error('Valid signup data should pass validation')
  }
})

test('Invalid email should fail validation', () => {
  const invalidData = {
    firstName: 'John',
    lastName: 'Doe',
    companyName: 'Acme Corp',
    country: 'United States',
    email: 'invalid-email',
    password: 'SecurePass123!',
    confirmPassword: 'SecurePass123!'
  }
  
  const result = validateData(signupSchema, invalidData)
  if (result.success) {
    throw new Error('Invalid email should fail validation')
  }
})

test('Weak password should fail validation', () => {
  const weakPasswordData = {
    firstName: 'John',
    lastName: 'Doe',
    companyName: 'Acme Corp',
    country: 'United States',
    email: '<EMAIL>',
    password: 'weak',
    confirmPassword: 'weak'
  }
  
  const result = validateData(signupSchema, weakPasswordData)
  if (result.success) {
    throw new Error('Weak password should fail validation')
  }
})

test('Mismatched passwords should fail validation', () => {
  const mismatchedData = {
    firstName: 'John',
    lastName: 'Doe',
    companyName: 'Acme Corp',
    country: 'United States',
    email: '<EMAIL>',
    password: 'SecurePass123!',
    confirmPassword: 'DifferentPass123!'
  }
  
  const result = validateData(signupSchema, mismatchedData)
  if (result.success) {
    throw new Error('Mismatched passwords should fail validation')
  }
})

// Login validation tests
test('Valid login data should pass validation', () => {
  const validData = {
    email: '<EMAIL>',
    password: 'SecurePass123!'
  }
  
  const result = validateData(loginSchema, validData)
  if (!result.success) {
    throw new Error('Valid login data should pass validation')
  }
})

test('Empty email should fail login validation', () => {
  const invalidData = {
    email: '',
    password: 'SecurePass123!'
  }
  
  const result = validateData(loginSchema, invalidData)
  if (result.success) {
    throw new Error('Empty email should fail validation')
  }
})

// Run tests if this file is executed directly
if (typeof window === 'undefined' && require.main === module) {
  console.log('Running validation tests...\n')
}
