import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface VulnerabilitySummaryProps {
  critical: number
  high: number
  medium: number
  low: number
  info: number
  className?: string
  title?: string
}

interface SummaryItemProps {
  label: string
  count: number
  color: 'red' | 'orange' | 'yellow' | 'blue' | 'green'
}

const SummaryItem: React.FC<SummaryItemProps> = ({ label, count, color }) => {
  const colorClasses = {
    red: 'text-red-600',
    orange: 'text-orange-600',
    yellow: 'text-yellow-600',
    blue: 'text-blue-600',
    green: 'text-green-600',
  }

  return (
    <div className="text-center">
      <div className={cn('text-2xl font-bold', colorClasses[color])}>
        {count}
      </div>
      <div className="text-sm text-gray-500">{label}</div>
    </div>
  )
}

export const VulnerabilitySummary: React.FC<VulnerabilitySummaryProps> = ({
  critical,
  high,
  medium,
  low,
  info,
  className,
  title = 'Vulnerability Summary',
}) => {
  const total = critical + high + medium + low + info

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {total > 0 ? (
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <SummaryItem label="Critical" count={critical} color="red" />
            <SummaryItem label="High" count={high} color="orange" />
            <SummaryItem label="Medium" count={medium} color="yellow" />
            <SummaryItem label="Low" count={low} color="blue" />
            <SummaryItem label="Info" count={info} color="green" />
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No vulnerability data available
          </div>
        )}
      </CardContent>
    </Card>
  )
}
