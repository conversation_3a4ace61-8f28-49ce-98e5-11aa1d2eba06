import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { handleApiError, NotFoundError, AuthorizationError } from '@/lib/errors'
import { scanEventManager } from '@/lib/scan-events'
import { db } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Await params
    const { id: scanId } = await params

    // Check if scan exists and belongs to user
    const scan = await db.scan.findUnique({
      where: { id: scanId },
      select: { 
        id: true, 
        userId: true, 
        status: true,
        targetUrl: true
      }
    })

    if (!scan) {
      throw new NotFoundError('Scan not found')
    }

    if (scan.userId !== currentUser.userId) {
      throw new AuthorizationError('Access denied')
    }

    // Create SSE response
    const responseStream = new ReadableStream({
      start(controller) {
        // Set up SSE headers
        const headers = {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control'
        }

        // Create a mock response object for the event manager
        const client = {
          write: (data: string) => {
            try {
              controller.enqueue(new TextEncoder().encode(data))
            } catch (error) {
              console.error('Error writing to SSE stream:', error)
            }
          },
          on: (event: string, callback: () => void) => {
            // Handle client disconnect
            if (event === 'close') {
              // This will be called when the stream is closed
              request.signal.addEventListener('abort', callback)
            }
          }
        }

        // Register client with scan event manager
        scanEventManager.registerClient(scanId, client)

        // Send initial connection message
        const initialMessage = {
          type: 'connected',
          scanId,
          data: { 
            message: 'Connected to scan updates',
            scanStatus: scan.status,
            targetUrl: scan.targetUrl
          },
          timestamp: new Date()
        }
        
        client.write(`data: ${JSON.stringify(initialMessage)}\n\n`)

        // Send heartbeat every 30 seconds to keep connection alive
        const heartbeatInterval = setInterval(() => {
          try {
            const heartbeat = {
              type: 'heartbeat',
              scanId,
              data: { timestamp: new Date() },
              timestamp: new Date()
            }
            client.write(`data: ${JSON.stringify(heartbeat)}\n\n`)
          } catch (error) {
            clearInterval(heartbeatInterval)
          }
        }, 30000)

        // Handle client disconnect
        request.signal.addEventListener('abort', () => {
          clearInterval(heartbeatInterval)
          scanEventManager.unregisterClient(scanId, client)
          try {
            controller.close()
          } catch (error) {
            // Stream already closed
          }
        })
      }
    })

    return new Response(responseStream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    })

  } catch (error) {
    return handleApiError(error)
  }
}
