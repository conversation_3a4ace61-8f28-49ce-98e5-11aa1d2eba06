/**
 * Test script to verify Nuclei integration works correctly
 * This script tests the actual Nuclei scanner integration without the full web application
 */

const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')

// Test configurations matching user requirements
const testConfigs = [
  {
    name: 'Web+API Basic Scan',
    target: 'https://httpbin.org',
    scanType: 'web-api',
    scanMode: 'basic',
    expectedTemplates: ['misconfiguration', 'exposed-panels', 'exposures', 'defaults-logins', 'miscellaneous', 'osint', 'ssl']
  },
  {
    name: 'Web+API Advanced Scan',
    target: 'https://httpbin.org',
    scanType: 'web-api', 
    scanMode: 'advanced',
    expectedTemplates: ['http', 'dast', 'javascript', 'cves']
  }
]

async function testNucleiCommand(config) {
  console.log(`\n🧪 Testing: ${config.name}`)
  console.log(`Target: ${config.target}`)
  console.log(`Expected templates: ${config.expectedTemplates.join(', ')}`)
  
  const outputFile = path.join(__dirname, `test-output-${Date.now()}.json`)
  
  // Build command args exactly as the application does
  const args = [
    '-u', config.target,
    '-t', config.expectedTemplates.join(','),
    '-jsonl',
    '-output', outputFile,
    '-silent',
    '-no-color',
    '-timeout', '30',
    '-concurrency', '10',
    '-rate-limit', '50',
    '-retries', '1',
    '-max-host-error', '3',
    '-disable-update-check',
    '-no-meta',
    '-no-timestamp'
  ]
  
  console.log(`Command: nuclei ${args.join(' ')}`)
  
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    const child = spawn('nuclei', args, { stdio: 'pipe' })
    
    let stdout = ''
    let stderr = ''
    
    child.stdout.on('data', (data) => {
      stdout += data.toString()
    })
    
    child.stderr.on('data', (data) => {
      stderr += data.toString()
    })
    
    child.on('close', (code) => {
      const duration = Math.round((Date.now() - startTime) / 1000)
      
      console.log(`⏱️  Duration: ${duration} seconds`)
      console.log(`📊 Exit code: ${code}`)
      
      if (stderr) {
        console.log(`📝 Stderr: ${stderr.substring(0, 200)}...`)
      }
      
      // Read and parse results
      let vulnerabilities = []
      try {
        if (fs.existsSync(outputFile)) {
          const content = fs.readFileSync(outputFile, 'utf-8')
          if (content.trim()) {
            const lines = content.trim().split('\n')
            vulnerabilities = lines.map(line => {
              try {
                return JSON.parse(line)
              } catch (e) {
                return null
              }
            }).filter(Boolean)
          }
          
          // Clean up
          fs.unlinkSync(outputFile)
        }
      } catch (error) {
        console.log(`⚠️  Error reading results: ${error.message}`)
      }
      
      console.log(`🔍 Vulnerabilities found: ${vulnerabilities.length}`)
      
      if (vulnerabilities.length > 0) {
        console.log(`📋 Sample vulnerabilities:`)
        vulnerabilities.slice(0, 3).forEach((vuln, index) => {
          console.log(`   ${index + 1}. ${vuln.info?.name || 'Unknown'} (${vuln.info?.severity || 'unknown'})`)
        })
      }
      
      if (code === 0 || code === 1 || code === 2) {
        console.log(`✅ Test passed!`)
        if (code === 2) {
          console.log(`ℹ️  Note: Exit code 2 indicates some template warnings (this is normal)`)
        }
        resolve({ success: true, vulnerabilities, duration, code })
      } else {
        console.log(`❌ Test failed with exit code ${code}`)
        console.log(`Error: ${stderr}`)
        reject(new Error(`Exit code ${code}: ${stderr}`))
      }
    })
    
    child.on('error', (error) => {
      console.log(`❌ Process error: ${error.message}`)
      reject(error)
    })
  })
}

async function runTests() {
  console.log('🚀 Starting Nuclei Integration Tests')
  console.log('=' .repeat(50))
  
  // Check if Nuclei is installed
  try {
    const child = spawn('nuclei', ['--version'], { stdio: 'pipe' })
    await new Promise((resolve, reject) => {
      child.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Nuclei is installed and accessible')
          resolve()
        } else {
          reject(new Error('Nuclei not found'))
        }
      })
      child.on('error', reject)
    })
  } catch (error) {
    console.error('❌ Nuclei is not installed or not accessible')
    console.error('Please install Nuclei first:')
    console.error('  go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest')
    process.exit(1)
  }
  
  let allPassed = true
  
  for (const config of testConfigs) {
    try {
      const result = await testNucleiCommand(config)
      
      // Validate results
      if (result.duration > 60) {
        console.log(`⚠️  Warning: Scan took ${result.duration}s (longer than expected)`)
      }
      
    } catch (error) {
      console.log(`❌ Test failed: ${error.message}`)
      allPassed = false
    }
  }
  
  console.log('\n' + '=' .repeat(50))
  if (allPassed) {
    console.log('🎉 All tests passed! Nuclei integration is working correctly.')
  } else {
    console.log('❌ Some tests failed. Please check the configuration.')
    process.exit(1)
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test suite failed:', error)
    process.exit(1)
  })
}

module.exports = { testNucleiCommand, runTests }
