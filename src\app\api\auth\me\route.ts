import { NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { getCurrentUser } from '@/lib/auth'
import { handleApiError, AuthenticationError, NotFoundError } from '@/lib/errors'

export async function GET() {
  try {
    // Get current user from token
    const currentUser = await getCurrentUser()

    if (!currentUser) {
      throw new AuthenticationError('Not authenticated')
    }

    // Fetch user data from database
    const user = await db.user.findUnique({
      where: { id: currentUser.userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        companyName: true,
        country: true,
        email: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    if (!user) {
      throw new NotFoundError('User not found')
    }

    return NextResponse.json(
      { user },
      { status: 200 }
    )

  } catch (error) {
    return handleApiError(error)
  }
}
