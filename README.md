# CTB Scanner - Vulnerability Scanning Platform

A comprehensive, production-ready vulnerability scanning platform built with Next.js 15, TypeScript, Prisma, MySQL, and Nuclei integration.

## 🚀 Features

### 🔐 Authentication & Security
- **Secure Authentication**: JWT-based authentication with HTTP-only cookies
- **Password Security**: Bcrypt hashing with salt rounds
- **Input Validation**: Comprehensive validation using Zod schemas
- **Rate Limiting**: Protection against brute force attacks and scan abuse
- **Security Headers**: CSRF protection, XSS prevention, and more
- **URL Validation**: Advanced URL sanitization and private IP blocking

### 🛡️ Vulnerability Scanning
- **Nuclei Integration**: Powered by Nuclei vulnerability scanner
- **Real-time Scanning**: Background job processing with status updates
- **Comprehensive Results**: Detailed vulnerability reports with severity classification
- **Asset Management**: Track and manage scanned assets
- **Scan History**: Complete audit trail of all scanning activities
- **Rate Limiting**: Configurable scan limits per user/hour

### 📊 Dashboard & Visualization
- **Interactive Charts**: Vulnerability distribution and trend analysis
- **Real-time Statistics**: Live dashboard with scan metrics
- **Asset Inventory**: Comprehensive asset management system
- **Vulnerability Database**: Searchable vulnerability repository
- **Export Capabilities**: JSON/CSV export of scan results

### 🏗️ Technical Excellence
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **Type Safety**: Full TypeScript implementation
- **Database**: MySQL with Prisma ORM
- **Error Handling**: Comprehensive error handling and logging
- **Background Jobs**: Scalable job queue system
- **API Documentation**: RESTful API with comprehensive endpoints

## 🛠️ Tech Stack

### Core Framework
- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS
- **Database**: MySQL with Prisma ORM
- **Authentication**: JWT with jose library

### Security & Validation
- **Validation**: Zod schemas with comprehensive rules
- **Password Hashing**: bcryptjs with salt rounds
- **Rate Limiting**: Custom implementation with IP tracking
- **URL Sanitization**: Advanced security validation

### Vulnerability Scanning
- **Scanner Engine**: Nuclei vulnerability scanner
- **Job Processing**: Background job queue system
- **Result Storage**: Structured vulnerability database
- **Asset Tracking**: Comprehensive asset inventory

### UI & Visualization
- **Charts**: Recharts for data visualization
- **Forms**: React Hook Form with validation
- **Icons**: Lucide React
- **Components**: Custom UI component library

### Development & Deployment
- **Type Safety**: Full TypeScript coverage
- **Database Migrations**: Prisma migrations
- **Error Handling**: Structured error management
- **API Design**: RESTful endpoints with OpenAPI compatibility

## 📋 Prerequisites

- Node.js 18+
- MySQL 8.0+
- npm or yarn
- **Nuclei Scanner** (for vulnerability scanning)

### Installing Nuclei

#### Option 1: Using Go (Recommended)
```bash
go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest
```

#### Option 2: Using Package Managers

**macOS (Homebrew):**
```bash
brew install nuclei
```

**Linux (Snap):**
```bash
sudo snap install nuclei
```

**Windows (Chocolatey):**
```bash
choco install nuclei
```

#### Option 3: Download Binary
Download the latest release from [Nuclei Releases](https://github.com/projectdiscovery/nuclei/releases)

#### Verify Installation
```bash
nuclei -version
```

#### Update Templates
```bash
nuclei -update-templates
```


## 🚀 Getting Started (Mac, Windows, Linux)

Follow these steps to set up the project on **macOS**, **Windows**, or **Linux**. Platform-specific commands are provided where needed.

### 1. Clone the repository

```bash
git clone <repository-url>
cd ctb-scanner
```

### 2. Install dependencies

```bash
npm install
```

### 3. Environment Setup

Create a `.env` file in the root directory and configure your database connection:

```env
# Database Configuration
DATABASE_URL="mysql://root:rootroot@localhost:3306/ctb-dev-2"

# JWT Secret (change in production)
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# Next.js Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-change-this-in-production"
```

### 4. Database Setup

#### Install MySQL

- **macOS:**
  ```bash
  brew install mysql
  brew services start mysql
  ```
- **Linux (Ubuntu/Debian):**
  ```bash
  sudo apt update
  sudo apt install mysql-server
  sudo systemctl start mysql
  ```
- **Windows:**
  - Download and install MySQL from [MySQL Installer](https://dev.mysql.com/downloads/installer/).
  - Start MySQL from the Services panel or MySQL Workbench.

#### Create Database

```sql
CREATE DATABASE ctb_dev_2;
CREATE USER 'root'@'localhost' IDENTIFIED BY 'rootroot';
GRANT ALL PRIVILEGES ON ctb_dev_2.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

#### Prisma Setup

```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev --name init

# (Optional) View database in Prisma Studio
npx prisma studio
```

### 5. Nuclei Scanner Setup

#### Install Nuclei

- **macOS:**
  ```bash
  brew install nuclei
  nuclei -update-templates
  ```
- **Linux:**
  ```bash
  sudo snap install nuclei
  nuclei -update-templates
  ```
- **Windows:**
  ```powershell
  choco install nuclei
  nuclei -update-templates
  ```
  Or download the binary from [Nuclei Releases](https://github.com/projectdiscovery/nuclei/releases) and add it to your PATH.

#### Verify Nuclei Installation

```bash
nuclei -version
```

#### (Optional) Set Nuclei Path
If you installed Nuclei in a custom location, set the path in your `.env`:

```env
NUCLEI_PATH="/usr/local/bin/nuclei" # or the path to nuclei.exe on Windows
```

### 6. Run the development server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
ctb-scanner/
├── public/                        # Static assets (SVGs, images)
├── prisma/                        # Database schema and migrations
│   ├── schema.prisma
│   └── migrations/
├── scripts/                       # Utility and test scripts
│   ├── diagnose-nuclei.js
│   ├── test-api.js
│   ├── test-db.js
│   ├── test-nuclei-integration.js
│   ├── test-real-time-logs.js
│   ├── test-scan.js
│   └── test-10-second-scan.md
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   ├── admin/
│   │   │   ├── assets/
│   │   │   ├── auth/
│   │   │   ├── dashboard/
│   │   │   ├── queue/
│   │   │   ├── scans/
│   │   │   └── vulnerabilities/
│   │   ├── dashboard/
│   │   │   ├── assets/
│   │   │   ├── profile/
│   │   │   ├── scan/
│   │   │   ├── scans/
│   │   │   ├── settings/
│   │   │   └── vulnerabilities/
│   │   ├── login/
│   │   ├── signup/
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   └── globals.css
│   ├── components/
│   │   ├── client-wrapper.tsx
│   │   ├── index.ts
│   │   ├── loading.tsx
│   │   ├── no-ssr.tsx
│   │   ├── protected-route.tsx
│   │   ├── sidebar.tsx
│   │   ├── features/
│   │   │   └── scan/
│   │   ├── layout/
│   │   │   ├── data-table.tsx
│   │   │   ├── index.ts
│   │   │   ├── page-container.tsx
│   │   │   └── page-header.tsx
│   │   └── ui/
│   │       ├── alert.tsx
│   │       ├── badge.tsx
│   │       ├── button.tsx
│   │       ├── card.tsx
│   │       ├── empty-state.tsx
│   │       ├── index.ts
│   │       ├── input.tsx
│   │       ├── label.tsx
│   │       ├── loading-spinner.tsx
│   │       ├── pagination.tsx
│   │       ├── status-badge.tsx
│   │       └── tabs.tsx
│   ├── contexts/
│   │   └── auth-context.tsx
│   ├── hooks/
│   │   └── use-scan-events.ts
│   ├── lib/
│   │   ├── auth.ts
│   │   ├── db.ts
│   │   ├── errors.ts
│   │   ├── job-queue.ts
│   │   ├── nuclei.ts
│   │   ├── process-manager.ts
│   │   ├── rate-limit.ts
│   │   ├── scan-events.ts
│   │   ├── security.ts
│   │   ├── utils.ts
│   │   ├── validations.ts
│   │   └── __tests__/
│   │       ├── auth.test.ts
│   │       └── validations.test.ts
│   └── middleware.ts
├── temp/
│   └── nuclei/                     # Temporary scan result files
├── .env.example                    # Example environment variables
├── eslint.config.mjs
├── next-env.d.ts
├── next.config.ts
├── NUCLEI_TEMPLATE_DESIGN.md
├── package.json
├── postcss.config.mjs
├── README.md
├── simple-test.json
├── test-nuclei-comparison.js
├── test-redirect-fix.json
├── tsconfig.json
```

## 🔐 Security Features

### Authentication
- JWT tokens with secure HTTP-only cookies
- Password hashing with bcrypt (12 salt rounds)
- Token expiration (7 days)
- Secure cookie configuration

### Rate Limiting
- 5 authentication attempts per 15 minutes
- 60 general requests per minute
- IP-based tracking

### Input Validation
- Comprehensive Zod schemas
- Password strength requirements
- Email format validation
- Input sanitization

### Security Headers
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Referrer-Policy: strict-origin-when-cross-origin
- X-XSS-Protection: 1; mode=block
- Content Security Policy
- Strict Transport Security

## 📊 Database Schema

### Users Table
```sql
CREATE TABLE users (
  id VARCHAR(191) PRIMARY KEY,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  company_name VARCHAR(200) NOT NULL,
  country VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  INDEX idx_email (email)
);
```

## 🛡️ API Endpoints

### Authentication Routes

#### POST /api/auth/signup
Register a new user account.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "companyName": "Acme Corp",
  "country": "United States",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "confirmPassword": "SecurePass123!"
}
```

#### POST /api/auth/login
Authenticate user and create session.

#### POST /api/auth/logout
Logout user and clear session.

#### GET /api/auth/me
Get current authenticated user information.

### Vulnerability Scanning Routes

#### POST /api/scans
Initiate a new vulnerability scan.

**Request Body:**
```json
{
  "url": "https://example.com",
  "severity": ["critical", "high", "medium", "low", "info"],
  "tags": ["cve", "xss", "sqli"],
  "templates": ["specific-template-id"],
  "excludeTemplates": ["template-to-exclude"]
}
```

**Response:**
```json
{
  "message": "Scan initiated successfully",
  "scanId": "scan_123",
  "status": "PENDING"
}
```

#### GET /api/scans
Get list of scans with pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 50)
- `status`: Filter by scan status

#### GET /api/scans/[id]
Get detailed scan results including vulnerabilities.

#### DELETE /api/scans/[id]
Delete a scan and its associated vulnerabilities.

### Asset Management Routes

#### GET /api/assets
Get list of assets with scan statistics.

**Query Parameters:**
- `page`: Page number
- `limit`: Items per page
- `status`: Filter by asset status
- `search`: Search by URL, domain, or title

#### POST /api/assets
Create a new asset manually.

#### GET /api/assets/[id]
Get detailed asset information with scan history.

#### PUT /api/assets/[id]
Update asset information.

#### DELETE /api/assets/[id]
Delete an asset and all associated data.

### Vulnerability Routes

#### GET /api/vulnerabilities
Get list of vulnerabilities across all assets.

**Query Parameters:**
- `page`: Page number
- `limit`: Items per page
- `severity`: Filter by severity level
- `search`: Search by name, template ID, or host

### Dashboard Routes

#### GET /api/dashboard/stats
Get comprehensive dashboard statistics.

**Response:**
```json
{
  "overview": {
    "totalAssets": 25,
    "totalScans": 150,
    "totalVulnerabilities": 45,
    "activeScans": 2
  },
  "vulnerabilitySeverityCount": {
    "CRITICAL": 5,
    "HIGH": 12,
    "MEDIUM": 18,
    "LOW": 8,
    "INFO": 2
  },
  "recentScans": [...],
  "topVulnerableAssets": [...],
  "activityData": [...]
}
```


### Queue Management & Concurrency

#### Scan Job Queue & Concurrency
- The backend uses a built-in job queue to manage scan execution.
- **Concurrency:**
  - Up to **3 scans** can run concurrently across all users.
  - Each user can have **only 1 active scan** (RUNNING or PENDING) at a time.
  - Additional scan requests are queued and processed automatically when slots are available.
- The queue is managed in the database and is cross-platform (works on Mac, Windows, Linux).
- Scan status is updated in real-time and can be monitored via the API or dashboard.

#### GET /api/queue/status
Get current job queue status and scan concurrency.

**Response:**
```json
{
  "queue": {
    "pending": 3,
    "running": 1,
    "completed": 147,
    "failed": 2
  },
  "concurrency": {
    "maxConcurrentScans": 3,
    "maxConcurrentScansPerUser": 1
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### POST /api/scans
Initiate a new scan. If the user already has an active scan, the request will be queued and started automatically when possible. The response will indicate the scan is queued (PENDING) if not started immediately.

#### Scan Statuses
- `PENDING`: Waiting in the queue.
- `RUNNING`: Actively being scanned.
- `COMPLETED`: Finished successfully.
- `FAILED`: Scan failed.
- `CANCELLED`: Scan was cancelled by the user or system.

#### Real-Time Updates
- Scan status and progress are available via the dashboard and API endpoints.
- The backend emits real-time events for scan status changes (see `/api/scans/[id]`).

## 🎯 Usage Guide

### Getting Started

1. **Access the Application**
   - Visit http://localhost:3000
   - Create an account or sign in

2. **Dashboard Overview**
   - View security statistics and metrics
   - Monitor recent scan activity
   - Track vulnerability trends

3. **Asset Management**
   - Navigate to "Assets" to view your inventory
   - Add assets manually or through scanning
   - Monitor asset security status

4. **Vulnerability Scanning**
   - Go to "Scan" page
   - Enter target URL (must be publicly accessible)
   - Select severity levels and scan options
   - Monitor scan progress in real-time

5. **Results Analysis**
   - View detailed vulnerability reports
   - Export results for further analysis
   - Track remediation progress

### Scanning Best Practices

- **Permission**: Only scan websites you own or have explicit permission to test
- **Rate Limiting**: Respect the built-in rate limits to avoid overwhelming targets
- **Scope**: Start with critical and high severity vulnerabilities
- **Regular Scanning**: Schedule regular scans to monitor security posture
- **Documentation**: Keep records of scan results for compliance

### Security Considerations

- **Network Access**: Ensure Nuclei can access target URLs
- **Firewall Rules**: Configure appropriate firewall rules for scanning
- **Resource Usage**: Monitor system resources during large scans
- **Data Privacy**: Scan results may contain sensitive information

## 🎨 UI Components

### Form Components
- `Button`: Customizable button with variants and loading states
- `Input`: Styled input field with validation and error handling
- `Label`: Accessible form labels with proper associations

### Layout Components
- `Sidebar`: Responsive navigation with collapsible mobile menu
- `ProtectedRoute`: Authentication-based route protection
- `ClientWrapper`: Hydration error prevention wrapper
- `NoSSR`: Server-side rendering bypass for dynamic content

### Visualization Components
- **Charts**: Recharts integration for vulnerability trends
- **Tables**: Sortable and filterable data tables
- **Status Indicators**: Real-time scan status displays
- **Progress Bars**: Scan progress visualization

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Database
npx prisma generate  # Generate Prisma client
npx prisma migrate   # Run migrations
npx prisma studio    # Open Prisma Studio
```

### Code Style
- ESLint configuration for Next.js
- TypeScript strict mode
- Prettier formatting (recommended)

## 🚀 Deployment

### Environment Variables (Production)
```env
# Database
DATABASE_URL="mysql://user:password@host:port/database"

# Authentication
JWT_SECRET="your-production-jwt-secret-256-bits"
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="your-production-nextauth-secret"

# Application
NODE_ENV="production"

# Nuclei Configuration (Optional)
NUCLEI_PATH="/usr/local/bin/nuclei"
NUCLEI_TEMPLATES_PATH="/opt/nuclei-templates"
```

### Production Deployment Steps

1. **Server Setup**
   ```bash
   # Install Node.js 18+
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # Install Nuclei
   go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest
   nuclei -update-templates
   ```

2. **Database Setup**
   ```bash
   # Create production database
   mysql -u root -p
   CREATE DATABASE ctb_scanner_prod;
   CREATE USER 'ctb_user'@'%' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON ctb_scanner_prod.* TO 'ctb_user'@'%';
   FLUSH PRIVILEGES;
   ```

3. **Application Deployment**
   ```bash
   # Clone and build
   git clone <repository-url>
   cd ctb-scanner
   npm install

   # Run migrations
   npx prisma migrate deploy
   npx prisma generate

   # Build application
   npm run build

   # Start with PM2 (recommended)
   npm install -g pm2
   pm2 start npm --name "ctb-scanner" -- start
   pm2 save
   pm2 startup
   ```

### Docker Deployment

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy application
COPY . .

# Build application
RUN npm run build

# Install Nuclei
RUN apk add --no-cache go git
RUN go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest

EXPOSE 3000

CMD ["npm", "start"]
```

### Production Considerations

#### Security
- Use HTTPS in production
- Configure proper firewall rules
- Implement network segmentation for scanning
- Regular security updates for Nuclei templates
- Monitor scan activities and resource usage

#### Performance
- Configure database connection pooling
- Implement Redis for session storage (optional)
- Set up load balancing for multiple instances
- Monitor memory usage during large scans
- Configure appropriate scan concurrency limits

#### Monitoring
- Set up application monitoring (e.g., New Relic, DataDog)
- Configure log aggregation (e.g., ELK stack)
- Monitor database performance
- Track scan queue metrics
- Set up alerting for failed scans

#### Backup & Recovery
- Regular database backups
- Backup scan results and configurations
- Document recovery procedures
- Test backup restoration regularly

### Scaling Considerations

#### Horizontal Scaling
- Separate scan workers from web application
- Use message queue (Redis/RabbitMQ) for job distribution
- Implement database read replicas
- Use CDN for static assets

#### Vertical Scaling
- Increase server resources for large scan volumes
- Optimize database queries and indexing
- Configure appropriate connection limits
- Monitor and tune garbage collection

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For support, please open an issue in the GitHub repository or contact the development team.
