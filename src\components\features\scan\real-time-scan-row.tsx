import React from 'react'
import { useScanEvents } from '@/hooks/use-scan-events'
import { ScanStatusBadge } from '@/components/ui'
import { Loader2, Shield } from 'lucide-react'

interface RealTimeScanRowProps {
  scan: any
  children: React.ReactNode
}

export function RealTimeScanRow({ scan, children }: RealTimeScanRowProps) {
  const {
    totalCount,
    severityCount,
    status: realtimeStatus,
    progress: realtimeProgress,
    isConnected
  } = useScanEvents(scan.id)

  // Use real-time data if available and scan is running
  const isRunning = scan.status === 'RUNNING' || scan.status === 'PENDING'
  const displayStatus = (isRunning && realtimeStatus) ? realtimeStatus.status : scan.status
  const displayTotalVulns = (isRunning && totalCount > 0) ? totalCount : scan.totalVulns
  const displaySeverityCount = (isRunning && Object.keys(severityCount).length > 0) ? severityCount : {
    CRITICAL: scan.criticalVulns,
    HIGH: scan.highVulns,
    MEDIUM: scan.mediumVulns,
    LOW: scan.lowVulns,
    INFO: scan.infoVulns
  }

  // Clone children and inject real-time data
  const enhancedChildren = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child as React.ReactElement<any>, {
        scan: {
          ...scan,
          status: displayStatus,
          totalVulns: displayTotalVulns,
          criticalVulns: displaySeverityCount.CRITICAL || 0,
          highVulns: displaySeverityCount.HIGH || 0,
          mediumVulns: displaySeverityCount.MEDIUM || 0,
          lowVulns: displaySeverityCount.LOW || 0,
          infoVulns: displaySeverityCount.INFO || 0
        }
      })
    }
    return child
  })

  return (
    <div className="relative">
      {enhancedChildren}
      
      {/* Real-time indicators overlay */}
      {isRunning && isConnected && (
        <div className="absolute top-2 right-2 flex items-center space-x-2">
          {realtimeProgress && (
            <div className="flex items-center space-x-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>Scanning...</span>
            </div>
          )}
          
          {totalCount > 0 && (
            <div className="flex items-center space-x-1 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
              <Shield className="h-3 w-3" />
              <span>+{totalCount}</span>
            </div>
          )}
          
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Real-time updates active" />
        </div>
      )}
    </div>
  )
}
