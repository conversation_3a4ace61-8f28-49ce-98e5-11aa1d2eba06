'use client'

import { useState } from 'react'
import { ScanStatus, DynamicScanForm } from '@/components/features/scan'
import { type ScanStatusType } from '@/components/ui'
import { useScanEvents } from '@/hooks/use-scan-events'

interface ScanResult {
  scanId: string
  status: ScanStatusType
  message: string
}

export default function ScanPage() {
  const [isScanning, setIsScanning] = useState(false)
  const [scanResult, setScanResult] = useState<ScanResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Real-time scan events
  const {
    status: realtimeStatus,
    progress: realtimeProgress,
    logs,
    isConnected
  } = useScanEvents(scanResult?.scanId || '')

  const handleScanSubmit = async (data: any) => {
    setIsScanning(true)
    setError(null)
    setScanResult(null)

    try {
      const response = await fetch('/api/scans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to start scan')
      }

      setScanResult({
        scanId: result.scanId,
        status: result.status,
        message: result.message
      })

      // Real-time events will handle status updates automatically

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start scan')
    } finally {
      setIsScanning(false)
    }
  }



  const handleReset = () => {
    setScanResult(null)
    setError(null)
  }

  const handleViewResults = () => {
    if (scanResult) {
      window.location.href = `/dashboard/scans/${scanResult.scanId}`
    }
  }

  return (
    <div className="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 -m-6 min-h-full">
      {/* Remove PageContainer and use full available space */}
      <div className="flex flex-col min-h-full">

        {/* Dynamic Scan Form */}
        <DynamicScanForm
          onSubmit={handleScanSubmit}
          isLoading={isScanning}
          error={error}
          onReset={handleReset}
          showResetButton={!!scanResult}
        />

        {/* Scan Status - Component under the form */}
        {scanResult && (
          <div className="p-6">
            <div className="max-w-7xl mx-auto">
              <ScanStatus
                scanId={scanResult.scanId}
                status={realtimeStatus?.status || scanResult.status}
                message={realtimeProgress?.message || realtimeStatus?.message || scanResult.message}
                onViewResults={(realtimeStatus?.status || scanResult.status) === 'COMPLETED' ? handleViewResults : undefined}
                logs={logs}
                isConnected={isConnected}
                showTerminal={true}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
