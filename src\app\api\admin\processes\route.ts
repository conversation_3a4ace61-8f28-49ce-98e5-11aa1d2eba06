import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { handleApiError } from '@/lib/errors'
import { processManager } from '@/lib/process-manager'

export async function GET(request: NextRequest) {
  try {
    // Authentication (you might want to add admin role check here)
    await requireAuth()

    // Get process statistics
    const stats = processManager.getStats()

    return NextResponse.json({
      message: 'Process status retrieved successfully',
      stats
    })

  } catch (error) {
    return handleApiError(error)
  }
}
