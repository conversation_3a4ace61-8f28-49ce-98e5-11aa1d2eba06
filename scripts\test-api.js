/**
 * API Integration Test Script
 * Tests the main API endpoints for the CTB Scanner
 */

const baseUrl = 'http://localhost:3000'

// Test user credentials
const testUser = {
  firstName: 'Test',
  lastName: 'User',
  companyName: 'Test Company',
  country: 'United States',
  email: `test-${Date.now()}@example.com`,
  password: 'TestPassword123!',
  confirmPassword: 'TestPassword123!'
}

let authCookie = ''
let scanId = ''
let assetId = ''

async function makeRequest(endpoint, options = {}) {
  const url = `${baseUrl}${endpoint}`
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...(authCookie && { 'Cookie': authCookie })
    }
  }

  const response = await fetch(url, { ...defaultOptions, ...options })
  
  // Extract cookies for authentication
  if (response.headers.get('set-cookie')) {
    authCookie = response.headers.get('set-cookie')
  }

  const data = await response.json()
  return { response, data }
}

async function testSignup() {
  console.log('🔐 Testing user signup...')
  
  const { response, data } = await makeRequest('/api/auth/signup', {
    method: 'POST',
    body: JSON.stringify(testUser)
  })

  if (response.ok) {
    console.log('✅ Signup successful')
    return true
  } else {
    console.error('❌ Signup failed:', data.error)
    return false
  }
}

async function testLogin() {
  console.log('🔐 Testing user login...')
  
  const { response, data } = await makeRequest('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: testUser.email,
      password: testUser.password
    })
  })

  if (response.ok) {
    console.log('✅ Login successful')
    return true
  } else {
    console.error('❌ Login failed:', data.error)
    return false
  }
}

async function testDashboardStats() {
  console.log('📊 Testing dashboard stats...')
  
  const { response, data } = await makeRequest('/api/dashboard/stats')

  if (response.ok) {
    console.log('✅ Dashboard stats retrieved')
    console.log(`   - Total assets: ${data.overview.totalAssets}`)
    console.log(`   - Total scans: ${data.overview.totalScans}`)
    return true
  } else {
    console.error('❌ Dashboard stats failed:', data.error)
    return false
  }
}

async function testCreateAsset() {
  console.log('🏢 Testing asset creation...')
  
  const { response, data } = await makeRequest('/api/assets', {
    method: 'POST',
    body: JSON.stringify({
      url: 'https://httpbin.org',
      title: 'Test Asset - HTTPBin',
      description: 'Test asset for API testing'
    })
  })

  if (response.ok) {
    assetId = data.asset.id
    console.log('✅ Asset created successfully')
    console.log(`   - Asset ID: ${assetId}`)
    return true
  } else {
    console.error('❌ Asset creation failed:', data.error)
    return false
  }
}

async function testInitiateScan() {
  console.log('🔍 Testing scan initiation...')
  
  const { response, data } = await makeRequest('/api/scans', {
    method: 'POST',
    body: JSON.stringify({
      url: 'https://httpbin.org',
      severity: ['info', 'low']
    })
  })

  if (response.ok) {
    scanId = data.scanId
    console.log('✅ Scan initiated successfully')
    console.log(`   - Scan ID: ${scanId}`)
    console.log(`   - Status: ${data.status}`)
    return true
  } else {
    console.error('❌ Scan initiation failed:', data.error)
    return false
  }
}

async function testGetScans() {
  console.log('📋 Testing scan list retrieval...')
  
  const { response, data } = await makeRequest('/api/scans?limit=5')

  if (response.ok) {
    console.log('✅ Scan list retrieved')
    console.log(`   - Total scans: ${data.pagination.total}`)
    console.log(`   - Current page: ${data.pagination.page}`)
    return true
  } else {
    console.error('❌ Scan list retrieval failed:', data.error)
    return false
  }
}

async function testGetAssets() {
  console.log('🏢 Testing asset list retrieval...')
  
  const { response, data } = await makeRequest('/api/assets?limit=5')

  if (response.ok) {
    console.log('✅ Asset list retrieved')
    console.log(`   - Total assets: ${data.pagination.total}`)
    return true
  } else {
    console.error('❌ Asset list retrieval failed:', data.error)
    return false
  }
}

async function testQueueStatus() {
  console.log('⏳ Testing queue status...')
  
  const { response, data } = await makeRequest('/api/queue/status')

  if (response.ok) {
    console.log('✅ Queue status retrieved')
    console.log(`   - Pending: ${data.queue.pending}`)
    console.log(`   - Running: ${data.queue.running}`)
    console.log(`   - Completed: ${data.queue.completed}`)
    return true
  } else {
    console.error('❌ Queue status failed:', data.error)
    return false
  }
}

async function runTests() {
  console.log('🧪 Starting API Integration Tests...\n')

  const tests = [
    testSignup,
    testLogin,
    testDashboardStats,
    testCreateAsset,
    testInitiateScan,
    testGetScans,
    testGetAssets,
    testQueueStatus
  ]

  let passed = 0
  let failed = 0

  for (const test of tests) {
    try {
      const result = await test()
      if (result) {
        passed++
      } else {
        failed++
      }
    } catch (error) {
      console.error(`❌ Test failed with error:`, error.message)
      failed++
    }
    console.log('') // Add spacing between tests
  }

  console.log('📊 Test Results:')
  console.log(`   ✅ Passed: ${passed}`)
  console.log(`   ❌ Failed: ${failed}`)
  console.log(`   📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`)

  if (failed === 0) {
    console.log('\n🎉 All tests passed! The API is working correctly.')
  } else {
    console.log('\n⚠️  Some tests failed. Please check the error messages above.')
    process.exit(1)
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test suite failed:', error)
    process.exit(1)
  })
}

module.exports = { runTests }
