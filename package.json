{"name": "ctb-scanner", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@prisma/client": "^6.13.0", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "clsx": "^2.1.1", "jose": "^6.0.12", "lucide-react": "^0.533.0", "next": "15.4.4", "prisma": "^6.13.0", "react": "19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "url-parse": "^1.5.10", "uuid": "^11.1.0", "validator": "^13.15.15", "zod": "^4.0.13"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}