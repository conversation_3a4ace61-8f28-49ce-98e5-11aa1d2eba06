import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError, NotFoundError, AuthorizationError } from '@/lib/errors'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Await params
    const { id } = await params

    // Get scan with vulnerabilities
    const scan = await db.scan.findUnique({
      where: { id },
      include: {
        asset: {
          select: {
            id: true,
            url: true,
            domain: true,
            title: true,
            description: true
          }
        },
        vulnerabilities: {
          orderBy: [
            { severity: 'desc' },
            { createdAt: 'desc' }
          ]
        }
      }
    })

    if (!scan) {
      throw new NotFoundError('Scan not found')
    }

    // Check ownership
    if (scan.userId !== currentUser.userId) {
      throw new AuthorizationError('Access denied')
    }

    return NextResponse.json({ scan })

  } catch (error) {
    return handleApiError(error)
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Await params
    const { id } = await params

    // Check if scan exists and belongs to user
    const scan = await db.scan.findUnique({
      where: { id },
      select: { id: true, userId: true, status: true }
    })

    if (!scan) {
      throw new NotFoundError('Scan not found')
    }

    if (scan.userId !== currentUser.userId) {
      throw new AuthorizationError('Access denied')
    }

    // Don't allow deletion of running scans
    if (scan.status === 'RUNNING') {
      return NextResponse.json(
        { error: 'Cannot delete a running scan' },
        { status: 400 }
      )
    }

    // Delete scan (vulnerabilities will be deleted due to cascade)
    await db.scan.delete({
      where: { id }
    })

    return NextResponse.json({
      message: 'Scan deleted successfully'
    })

  } catch (error) {
    return handleApiError(error)
  }
}
