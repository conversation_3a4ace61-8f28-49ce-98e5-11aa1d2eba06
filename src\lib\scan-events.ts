import { EventEmitter } from 'events'
import { db } from './db'
import { NucleiResult } from './nuclei'

export interface ScanEvent {
  type: 'vulnerability' | 'progress' | 'status' | 'error' | 'complete' | 'log'
  scanId: string
  data: any
  timestamp: Date
}

export interface VulnerabilityEvent extends ScanEvent {
  type: 'vulnerability'
  data: {
    vulnerability: any // Processed vulnerability data
    totalCount: number
    severityCount: Record<string, number>
  }
}

export interface ProgressEvent extends ScanEvent {
  type: 'progress'
  data: {
    message: string
    percentage?: number
  }
}

export interface StatusEvent extends ScanEvent {
  type: 'status'
  data: {
    status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
    message?: string
  }
}

export interface ErrorEvent extends ScanEvent {
  type: 'error'
  data: {
    error: string
    message: string
  }
}

export interface CompleteEvent extends ScanEvent {
  type: 'complete'
  data: {
    totalVulnerabilities: number
    duration: number
    severityCount: Record<string, number>
  }
}

export interface LogEvent extends ScanEvent {
  type: 'log'
  data: {
    message: string
    stream: 'stdout' | 'stderr'
    timestamp: Date
  }
}

class ScanEventManager extends EventEmitter {
  private scanClients = new Map<string, Set<any>>() // scanId -> Set of SSE clients

  constructor() {
    super()

    // Add error handler to prevent uncaught exceptions
    this.on('error', (error) => {
      console.error('ScanEventManager error (handled):', error)
      // Don't re-throw - just log it
    })
  }

  /**
   * Register a client for scan updates
   */
  registerClient(scanId: string, client: any) {
    if (!this.scanClients.has(scanId)) {
      this.scanClients.set(scanId, new Set())
    }
    this.scanClients.get(scanId)!.add(client)

    // Clean up when client disconnects
    client.on('close', () => {
      this.unregisterClient(scanId, client)
    })
  }

  /**
   * Unregister a client
   */
  unregisterClient(scanId: string, client: any) {
    const clients = this.scanClients.get(scanId)
    if (clients) {
      clients.delete(client)
      if (clients.size === 0) {
        this.scanClients.delete(scanId)
      }
    }
  }

  /**
   * Emit a vulnerability found event
   */
  async emitVulnerability(scanId: string, nucleiResult: NucleiResult) {
    try {
      // Process and save vulnerability to database
      const vulnerability = await this.processAndSaveVulnerability(scanId, nucleiResult)
      
      // Get updated counts
      const counts = await this.getScanCounts(scanId)
      
      const event: VulnerabilityEvent = {
        type: 'vulnerability',
        scanId,
        data: {
          vulnerability,
          totalCount: counts.total,
          severityCount: counts.severityCount
        },
        timestamp: new Date()
      }

      this.broadcastToClients(scanId, event)
      this.emit('vulnerability', event)
    } catch (error) {
      console.error('Error processing vulnerability:', error)
      this.emitError(scanId, 'Failed to process vulnerability', error instanceof Error ? error.message : 'Unknown error')
    }
  }

  /**
   * Emit a progress update
   */
  emitProgress(scanId: string, message: string, percentage?: number) {
    const event: ProgressEvent = {
      type: 'progress',
      scanId,
      data: { message, percentage },
      timestamp: new Date()
    }

    this.broadcastToClients(scanId, event)
    this.emit('progress', event)
  }

  /**
   * Emit a status change
   */
  async emitStatus(scanId: string, status: StatusEvent['data']['status'], message?: string) {
    // Update scan status in database
    try {
      await db.scan.update({
        where: { id: scanId },
        data: { 
          status,
          ...(status === 'RUNNING' && { startedAt: new Date() }),
          ...(status === 'COMPLETED' && { completedAt: new Date() }),
          ...(status === 'FAILED' && { completedAt: new Date() }),
          ...(status === 'CANCELLED' && { completedAt: new Date() })
        }
      })
    } catch (error) {
      console.error('Error updating scan status:', error)
    }

    const event: StatusEvent = {
      type: 'status',
      scanId,
      data: { status, message },
      timestamp: new Date()
    }

    this.broadcastToClients(scanId, event)
    this.emit('status', event)
  }

  /**
   * Emit an error
   */
  emitError(scanId: string, error: string, message: string) {
    const event: ErrorEvent = {
      type: 'error',
      scanId,
      data: { error, message },
      timestamp: new Date()
    }

    this.broadcastToClients(scanId, event)
    this.emit('error', event)
  }

  /**
   * Emit a log message from Nuclei
   */
  emitLog(scanId: string, message: string, stream: 'stdout' | 'stderr') {
    const event: LogEvent = {
      type: 'log',
      scanId,
      data: {
        message,
        stream,
        timestamp: new Date()
      },
      timestamp: new Date()
    }

    this.broadcastToClients(scanId, event)
    this.emit('log', event)
  }

  /**
   * Emit scan completion
   */
  async emitComplete(scanId: string, duration: number) {
    const counts = await this.getScanCounts(scanId)
    
    // Update final scan statistics
    try {
      await db.scan.update({
        where: { id: scanId },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
          duration,
          totalVulns: counts.total,
          criticalVulns: counts.severityCount.CRITICAL || 0,
          highVulns: counts.severityCount.HIGH || 0,
          mediumVulns: counts.severityCount.MEDIUM || 0,
          lowVulns: counts.severityCount.LOW || 0,
          infoVulns: counts.severityCount.INFO || 0
        }
      })
    } catch (error) {
      console.error('Error updating final scan stats:', error)
    }

    const event: CompleteEvent = {
      type: 'complete',
      scanId,
      data: {
        totalVulnerabilities: counts.total,
        duration,
        severityCount: counts.severityCount
      },
      timestamp: new Date()
    }

    this.broadcastToClients(scanId, event)
    this.emit('complete', event)
  }

  /**
   * Broadcast event to all clients listening to a scan
   */
  private broadcastToClients(scanId: string, event: ScanEvent) {
    const clients = this.scanClients.get(scanId)
    if (!clients) return

    const eventData = `data: ${JSON.stringify(event)}\n\n`
    
    clients.forEach(client => {
      try {
        client.write(eventData)
      } catch (error) {
        console.error('Error sending event to client:', error)
        this.unregisterClient(scanId, client)
      }
    })
  }

  /**
   * Process and save a single vulnerability
   */
  private async processAndSaveVulnerability(scanId: string, nucleiResult: NucleiResult) {
    // Get scan info for assetId
    const scan = await db.scan.findUnique({
      where: { id: scanId },
      select: { assetId: true }
    })

    // Helper function to safely truncate large text fields
    const truncateText = (text: string | null | undefined, maxLength: number = 16777215): string | null => {
      if (!text) return null
      if (text.length <= maxLength) return text
      return text.substring(0, maxLength - 100) + '\n\n[... TRUNCATED - Response too large for database ...]'
    }

    const vulnerability: any = {
      templateId: nucleiResult['template-id'],
      name: nucleiResult.info.name,
      severity: this.normalizeSeverity(nucleiResult.info.severity),
      description: truncateText(nucleiResult.info.description),
      reference: nucleiResult.info.reference ? JSON.stringify(nucleiResult.info.reference) : null,
      tags: nucleiResult.info.tags ? JSON.stringify(nucleiResult.info.tags) : null,
      matcher: truncateText(nucleiResult.matcher),
      extractedResults: nucleiResult['extracted-results'] ? JSON.stringify(nucleiResult['extracted-results']) : null,
      request: truncateText(nucleiResult.request),
      response: truncateText(nucleiResult.response),
      curlCommand: truncateText(nucleiResult['curl-command']),
      host: nucleiResult.host,
      matchedAt: nucleiResult['matched-at'],
      timestamp: new Date(nucleiResult.timestamp),
      scanId
    }

    if (scan?.assetId) {
      vulnerability.assetId = scan.assetId
    }

    // Save to database
    const savedVulnerability = await db.vulnerability.create({
      data: vulnerability
    })

    return savedVulnerability
  }

  /**
   * Get current vulnerability counts for a scan
   */
  private async getScanCounts(scanId: string) {
    const vulnerabilities = await db.vulnerability.findMany({
      where: { scanId },
      select: { severity: true }
    })

    const severityCount = vulnerabilities.reduce((acc, vuln) => {
      acc[vuln.severity] = (acc[vuln.severity] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      total: vulnerabilities.length,
      severityCount
    }
  }

  /**
   * Normalize severity to match database enum
   */
  private normalizeSeverity(severity: string): 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW' | 'INFO' | 'UNKNOWN' {
    const normalized = severity.toUpperCase()
    if (['CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO', 'UNKNOWN'].includes(normalized)) {
      return normalized as 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW' | 'INFO' | 'UNKNOWN'
    }
    return 'UNKNOWN'
  }

  /**
   * Get active scan clients count
   */
  getActiveClientsCount(): number {
    return Array.from(this.scanClients.values()).reduce((total, clients) => total + clients.size, 0)
  }

  /**
   * Get scans with active clients
   */
  getActiveScans(): string[] {
    return Array.from(this.scanClients.keys())
  }
}

// Singleton instance
export const scanEventManager = new ScanEventManager()

// Add global error handlers to prevent server crashes during demos
if (typeof process !== 'undefined') {
  process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception (handled):', error)
    // Don't exit the process - keep the demo running
  })

  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection (handled):', reason)
    // Don't exit the process - keep the demo running
  })
}
