import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth, hashPassword, verifyPassword } from '@/lib/auth'
import { changePasswordSchema, validateData } from '@/lib/validations'
import { handleApiError, NotFoundError, AuthenticationError } from '@/lib/errors'

export async function PUT(request: NextRequest) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Parse request body
    const body = await request.json()

    // Validate input data
    const validation = validateData(changePasswordSchema, body)
    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validation.errors
        },
        { status: 400 }
      )
    }

    const { currentPassword, newPassword } = validation.data

    // Get user with password
    const user = await db.user.findUnique({
      where: { id: currentUser.userId },
      select: {
        id: true,
        password: true,
      }
    })

    if (!user) {
      throw new NotFoundError('User not found')
    }

    // Verify current password
    const isCurrentPasswordValid = await verifyPassword(currentPassword, user.password)
    if (!isCurrentPasswordValid) {
      throw new AuthenticationError('Current password is incorrect')
    }

    // Hash new password
    const hashedNewPassword = await hashPassword(newPassword)

    // Update password
    await db.user.update({
      where: { id: currentUser.userId },
      data: {
        password: hashedNewPassword,
        updatedAt: new Date(),
      }
    })

    return NextResponse.json(
      {
        message: 'Password changed successfully',
      },
      { status: 200 }
    )

  } catch (error) {
    return handleApiError(error)
  }
}
