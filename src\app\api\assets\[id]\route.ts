import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError, NotFoundError, AuthorizationError } from '@/lib/errors'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Await params
    const { id } = await params

    // Get asset with detailed information
    const asset = await db.asset.findUnique({
      where: { id },
      include: {
        scans: {
          orderBy: {
            createdAt: 'desc'
          },
          select: {
            id: true,
            status: true,
            startedAt: true,
            completedAt: true,
            totalVulns: true,
            criticalVulns: true,
            highVulns: true,
            mediumVulns: true,
            lowVulns: true,
            infoVulns: true,
            createdAt: true
          }
        },
        vulnerabilities: {
          orderBy: [
            { severity: 'desc' },
            { timestamp: 'desc' }
          ],
          select: {
            id: true,
            templateId: true,
            name: true,
            severity: true,
            description: true,
            host: true,
            matchedAt: true,
            timestamp: true
          }
        }
      }
    })

    if (!asset) {
      throw new NotFoundError('Asset not found')
    }

    // Check ownership
    if (asset.userId !== currentUser.userId) {
      throw new AuthorizationError('Access denied')
    }

    return NextResponse.json({ asset })

  } catch (error) {
    return handleApiError(error)
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Await params
    const { id } = await params

    // Check if asset exists and belongs to user
    const existingAsset = await db.asset.findUnique({
      where: { id },
      select: { id: true, userId: true }
    })

    if (!existingAsset) {
      throw new NotFoundError('Asset not found')
    }

    if (existingAsset.userId !== currentUser.userId) {
      throw new AuthorizationError('Access denied')
    }

    // Parse request body
    const body = await request.json()
    const { title, description, status } = body

    // Update asset
    const updatedAsset = await db.asset.update({
      where: { id },
      data: {
        ...(title && { title }),
        ...(description !== undefined && { description }),
        ...(status && { status })
      },
      include: {
        _count: {
          select: {
            scans: true,
            vulnerabilities: true
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Asset updated successfully',
      asset: updatedAsset
    })

  } catch (error) {
    return handleApiError(error)
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Await params
    const { id } = await params

    // Check if asset exists and belongs to user
    const asset = await db.asset.findUnique({
      where: { id },
      select: { id: true, userId: true }
    })

    if (!asset) {
      throw new NotFoundError('Asset not found')
    }

    if (asset.userId !== currentUser.userId) {
      throw new AuthorizationError('Access denied')
    }

    // Delete asset (scans and vulnerabilities will be deleted due to cascade)
    await db.asset.delete({
      where: { id }
    })

    return NextResponse.json({
      message: 'Asset deleted successfully'
    })

  } catch (error) {
    return handleApiError(error)
  }
}
