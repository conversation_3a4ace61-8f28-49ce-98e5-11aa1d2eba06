const { nucleiScanner } = require('../src/lib/nuclei')
const { scanEventManager } = require('../src/lib/scan-events')

async function testRealTimeLogs() {
  console.log('🧪 Testing Real-time Nuclei Logs')
  console.log('================================')

  try {
    // Check if Nuclei is installed
    console.log('1. Checking Nuclei installation...')
    const isInstalled = await nucleiScanner.checkNucleiInstallation()
    
    if (!isInstalled) {
      console.error('❌ Nuclei is not installed or not accessible')
      console.log('Please install Nuclei first:')
      console.log('  go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest')
      process.exit(1)
    }
    
    console.log('✅ Nuclei is installed and accessible')

    // Set up event listeners
    console.log('\n2. Setting up event listeners...')
    
    const testScanId = 'test-scan-' + Date.now()
    const logs = []
    
    scanEventManager.on('log', (event) => {
      if (event.scanId === testScanId) {
        logs.push(event.data)
        console.log(`📝 [${event.data.stream}] ${event.data.message.trim()}`)
      }
    })

    scanEventManager.on('progress', (event) => {
      if (event.scanId === testScanId) {
        console.log(`📊 Progress: ${event.data.message}`)
      }
    })

    scanEventManager.on('status', (event) => {
      if (event.scanId === testScanId) {
        console.log(`🔄 Status: ${event.data.status} - ${event.data.message || ''}`)
      }
    })

    scanEventManager.on('vulnerability', (event) => {
      if (event.scanId === testScanId) {
        console.log(`🚨 Vulnerability found: ${event.data.vulnerability.templateId}`)
      }
    })

    scanEventManager.on('complete', (event) => {
      if (event.scanId === testScanId) {
        console.log(`✅ Scan completed: ${event.data.totalVulnerabilities} vulnerabilities found`)
      }
    })

    // Test scan on a safe target
    console.log('\n3. Running test scan on httpbin.org...')
    const testUrl = 'https://httpbin.org'
    
    const scanResult = await nucleiScanner.scan({
      target: testUrl,
      severity: ['info', 'low'],
      timeout: 30,
      concurrency: 5,
      rateLimit: 50
    }, testScanId)

    console.log('\n4. Scan Results:')
    console.log(`   Target: ${testUrl}`)
    console.log(`   Vulnerabilities: ${scanResult.vulnerabilities.length}`)
    console.log(`   Duration: ${scanResult.stats.duration}s`)
    console.log(`   Total logs captured: ${logs.length}`)
    
    if (logs.length > 0) {
      console.log('\n5. Sample logs:')
      logs.slice(0, 5).forEach((log, index) => {
        console.log(`   ${index + 1}. [${log.stream}] ${log.message.trim()}`)
      })
      
      if (logs.length > 5) {
        console.log(`   ... and ${logs.length - 5} more logs`)
      }
    }

    console.log('\n✅ Real-time logs test completed successfully!')
    console.log('You should now see Nuclei logs in real-time when running scans through the web interface.')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    process.exit(1)
  }
}

// Run the test
testRealTimeLogs()
