import { z } from 'zod'

// Common validation patterns
const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Please enter a valid email address')
  .max(255, 'Email must be less than 255 characters')

const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .max(100, 'Password must be less than 100 characters')
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
  )

const nameSchema = z
  .string()
  .min(1, 'This field is required')
  .max(100, 'Must be less than 100 characters')
  .regex(/^[a-zA-Z\s'-]+$/, 'Only letters, spaces, hyphens, and apostrophes are allowed')

const companyNameSchema = z
  .string()
  .min(1, 'Company name is required')
  .max(200, 'Company name must be less than 200 characters')
  .regex(/^[a-zA-Z0-9\s&.,'-]+$/, 'Company name contains invalid characters')

const countrySchema = z
  .string()
  .min(1, 'Country is required')
  .max(100, 'Country name must be less than 100 characters')

// Signup validation schema
export const signupSchema = z
  .object({
    firstName: nameSchema,
    lastName: nameSchema,
    companyName: companyNameSchema,
    country: countrySchema,
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  })

// Login validation schema
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
})

// User profile update schema (without password)
export const updateProfileSchema = z.object({
  firstName: nameSchema,
  lastName: nameSchema,
  companyName: companyNameSchema,
  country: countrySchema,
})

// Password change schema
export const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: passwordSchema,
    confirmNewPassword: z.string().min(1, 'Please confirm your new password'),
  })
  .refine((data) => data.newPassword === data.confirmNewPassword, {
    message: 'New passwords do not match',
    path: ['confirmNewPassword'],
  })
  .refine((data) => data.currentPassword !== data.newPassword, {
    message: 'New password must be different from current password',
    path: ['newPassword'],
  })

// Scan validation schemas
export const scanUrlSchema = z.object({
  // Target input - can be URL, IP, or file path for bulk scans
  target: z
    .string()
    .min(1, 'Target is required'),

  // Scan type: web-api or network
  scanType: z
    .enum(['web-api', 'network'])
    .optional()
    .default('web-api'),

  // Scan mode: basic or advanced
  scanMode: z
    .enum(['basic', 'advanced'])
    .optional()
    .default('basic'),

  // Input type: single or bulk
  inputType: z
    .enum(['single', 'bulk'])
    .optional()
    .default('single'),

  // Legacy fields for backward compatibility
  url: z
    .string()
    .optional(),

  severity: z
    .array(z.enum(['critical', 'high', 'medium', 'low', 'info', 'unknown']))
    .optional()
    .default(['critical', 'high', 'medium', 'low', 'info', 'unknown']),

  tags: z
    .array(z.string())
    .optional(),

  templates: z
    .array(z.string())
    .optional(),

  excludeTemplates: z
    .array(z.string())
    .optional(),
})

export const assetSchema = z.object({
  url: z
    .string()
    .min(1, 'URL is required')
    .url('Please enter a valid URL'),

  title: z
    .string()
    .max(500, 'Title must be less than 500 characters')
    .optional(),

  description: z
    .string()
    .max(1000, 'Description must be less than 1000 characters')
    .optional(),
})

// Type exports for TypeScript
export type SignupInput = z.infer<typeof signupSchema>
export type LoginInput = z.infer<typeof loginSchema>
export type UpdateProfileInput = z.infer<typeof updateProfileSchema>
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>
export type ScanUrlInput = z.infer<typeof scanUrlSchema>
export type AssetInput = z.infer<typeof assetSchema>

// Validation helper function
export function validateData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: Record<string, string[]> } {
  const result = schema.safeParse(data)
  
  if (result.success) {
    return { success: true, data: result.data }
  }
  
  const errors: Record<string, string[]> = {}
  result.error.errors.forEach((error) => {
    const path = error.path.join('.')
    if (!errors[path]) {
      errors[path] = []
    }
    errors[path].push(error.message)
  })
  
  return { success: false, errors }
}
