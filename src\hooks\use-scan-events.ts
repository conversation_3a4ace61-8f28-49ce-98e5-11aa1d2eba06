import { useEffect, useState, useRef } from 'react'

export interface ScanEvent {
  type: 'vulnerability' | 'progress' | 'status' | 'error' | 'complete' | 'connected' | 'heartbeat' | 'log'
  scanId: string
  data: any
  timestamp: string
}

export interface VulnerabilityData {
  vulnerability: any
  totalCount: number
  severityCount: Record<string, number>
}

export interface ScanStatus {
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  message?: string
}

export interface ScanProgress {
  message: string
  percentage?: number
}

export interface ScanError {
  error: string
  message: string
}

export interface ScanComplete {
  totalVulnerabilities: number
  duration: number
  severityCount: Record<string, number>
}

export interface ScanLog {
  message: string
  stream: 'stdout' | 'stderr'
  timestamp: Date | string // Can be Date object or string when serialized over WebSocket
}

export interface UseScanEventsReturn {
  vulnerabilities: any[]
  totalCount: number
  severityCount: Record<string, number>
  status: ScanStatus | null
  progress: ScanProgress | null
  error: ScanError | null
  complete: ScanComplete | null
  logs: ScanLog[]
  isConnected: boolean
  connectionError: string | null
}

export function useScanEvents(scanId: string): UseScanEventsReturn {
  const [vulnerabilities, setVulnerabilities] = useState<any[]>([])
  const [totalCount, setTotalCount] = useState(0)
  const [severityCount, setSeverityCount] = useState<Record<string, number>>({})
  const [status, setStatus] = useState<ScanStatus | null>(null)
  const [progress, setProgress] = useState<ScanProgress | null>(null)
  const [error, setError] = useState<ScanError | null>(null)
  const [complete, setComplete] = useState<ScanComplete | null>(null)
  const [logs, setLogs] = useState<ScanLog[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  
  const eventSourceRef = useRef<EventSource | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5

  const connect = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
    }

    try {
      const eventSource = new EventSource(`/api/scans/${scanId}/events`)
      eventSourceRef.current = eventSource

      eventSource.onopen = () => {
        setIsConnected(true)
        setConnectionError(null)
        reconnectAttempts.current = 0
      }

      eventSource.onmessage = (event) => {
        try {
          const scanEvent: ScanEvent = JSON.parse(event.data)
          
          switch (scanEvent.type) {
            case 'connected':
              console.log('Connected to scan events:', scanEvent.data)
              break
              
            case 'vulnerability':
              const vulnData = scanEvent.data as VulnerabilityData
              setVulnerabilities(prev => [...prev, vulnData.vulnerability])
              setTotalCount(vulnData.totalCount)
              setSeverityCount(vulnData.severityCount)
              break
              
            case 'progress':
              setProgress(scanEvent.data as ScanProgress)
              break
              
            case 'status':
              setStatus(scanEvent.data as ScanStatus)
              break
              
            case 'error':
              setError(scanEvent.data as ScanError)
              break
              
            case 'complete':
              setComplete(scanEvent.data as ScanComplete)
              setStatus({ status: 'COMPLETED', message: 'Scan completed successfully' })
              break

            case 'log':
              const logData = scanEvent.data as ScanLog
              // Ensure timestamp is properly handled (could be string from WebSocket)
              const processedLog: ScanLog = {
                ...logData,
                timestamp: typeof logData.timestamp === 'string'
                  ? new Date(logData.timestamp)
                  : logData.timestamp
              }
              setLogs(prev => [...prev, processedLog])
              break

            case 'heartbeat':
              // Keep connection alive
              break
              
            default:
              console.log('Unknown scan event type:', scanEvent.type)
          }
        } catch (error) {
          console.error('Error parsing scan event:', error)
        }
      }

      eventSource.onerror = (event) => {
        console.error('SSE connection error for scan:', scanId, event)
        setIsConnected(false)
        
        // Attempt to reconnect with exponential backoff
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.pow(2, reconnectAttempts.current) * 1000 // 1s, 2s, 4s, 8s, 16s
          reconnectAttempts.current++
          
          setConnectionError(`Connection lost. Reconnecting in ${delay / 1000}s... (${reconnectAttempts.current}/${maxReconnectAttempts})`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, delay)
        } else {
          setConnectionError('Connection failed after multiple attempts. Please refresh the page.')
        }
      }

    } catch (error) {
      console.error('Error creating SSE connection:', error)
      setConnectionError('Failed to establish connection')
      setIsConnected(false)
    }
  }

  useEffect(() => {
    if (!scanId) return

    connect()

    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
        eventSourceRef.current = null
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
        reconnectTimeoutRef.current = null
      }
    }
  }, [scanId])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
    }
  }, [])

  return {
    vulnerabilities,
    totalCount,
    severityCount,
    status,
    progress,
    error,
    complete,
    logs,
    isConnected,
    connectionError
  }
}
