// src/lib/api/scans.ts

export async function getScans({ page = 1, limit = 10, status = '' }: { page?: number; limit?: number; status?: string }) {
  const params = new URLSearchParams({ page: String(page), limit: String(limit) })
  if (status) params.append('status', status)
  const res = await fetch(`/api/scans?${params}`)
  if (!res.ok) throw new Error('Failed to fetch scans')
  return res.json()
}

export async function cancelScan(scanId: string) {
  const res = await fetch(`/api/scans/${scanId}/cancel`, { method: 'POST' })
  const data = await res.json()
  if (!res.ok) throw new Error(data.error || 'Failed to cancel scan')
  return data
}

export async function exportScans(scans: any[], format: 'json' | 'csv') {
  const exportedAt = new Date().toISOString()
  if (format === 'json') {
    const blob = new Blob([JSON.stringify({ scans, exportedAt }, null, 2)], { type: 'application/json' })
    return blob
  } else {
    const csvHeaders = ['ID', 'Target URL', 'Status', 'Total Vulnerabilities', 'Critical', 'High', 'Medium', 'Low', 'Info', 'Created', 'Duration']
    const csvRows = scans.map(scan => [
      scan.id,
      scan.targetUrl,
      scan.status,
      scan.totalVulns,
      scan.criticalVulns,
      scan.highVulns,
      scan.mediumVulns,
      scan.lowVulns,
      scan.infoVulns,
      new Date(scan.createdAt).toLocaleString(),
      scan.duration ? `${scan.duration}s` : 'N/A'
    ])
    const csvContent = [csvHeaders, ...csvRows].map(row => row.map(field => `"${field}"`).join(',')).join('\n')
    const blob = new Blob([csvContent], { type: 'text/csv' })
    return blob
  }
}
