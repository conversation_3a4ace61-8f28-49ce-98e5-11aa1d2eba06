import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError } from '@/lib/errors'

export async function GET(request: NextRequest) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Get basic counts
    const [
      totalAssets,
      totalScans,
      totalVulnerabilities,
      recentScans,
      vulnerabilityStats,
      scanStatusStats,
      topVulnerableAssets
    ] = await Promise.all([
      // Total assets
      db.asset.count({
        where: { userId: currentUser.userId }
      }),

      // Total scans
      db.scan.count({
        where: { userId: currentUser.userId }
      }),

      // Total vulnerabilities
      db.vulnerability.count({
        where: {
          scan: {
            userId: currentUser.userId
          }
        }
      }),

      // Recent scans (last 7 days)
      db.scan.findMany({
        where: {
          userId: currentUser.userId,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          asset: {
            select: {
              domain: true,
              title: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      }),

      // Vulnerability severity distribution
      db.vulnerability.groupBy({
        by: ['severity'],
        where: {
          scan: {
            userId: currentUser.userId
          }
        },
        _count: {
          severity: true
        }
      }),

      // Scan status distribution
      db.scan.groupBy({
        by: ['status'],
        where: {
          userId: currentUser.userId
        },
        _count: {
          status: true
        }
      }),

      // Top vulnerable assets
      db.asset.findMany({
        where: {
          userId: currentUser.userId
        },
        include: {
          _count: {
            select: {
              vulnerabilities: true
            }
          }
        },
        orderBy: {
          vulnerabilities: {
            _count: 'desc'
          }
        },
        take: 5
      })
    ])

    // Process vulnerability stats
    const vulnerabilitySeverityCount = {
      CRITICAL: 0,
      HIGH: 0,
      MEDIUM: 0,
      LOW: 0,
      INFO: 0,
      UNKNOWN: 0
    }

    vulnerabilityStats.forEach(stat => {
      vulnerabilitySeverityCount[stat.severity as keyof typeof vulnerabilitySeverityCount] = stat._count.severity
    })

    // Process scan status stats
    const scanStatusCount = {
      PENDING: 0,
      RUNNING: 0,
      COMPLETED: 0,
      FAILED: 0,
      CANCELLED: 0
    }

    scanStatusStats.forEach(stat => {
      scanStatusCount[stat.status as keyof typeof scanStatusCount] = stat._count.status
    })

    // Get scan activity for the last 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    const scanActivity = await db.scan.findMany({
      where: {
        userId: currentUser.userId,
        createdAt: {
          gte: thirtyDaysAgo
        }
      },
      select: {
        createdAt: true,
        status: true,
        totalVulns: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    // Group scan activity by day
    const activityByDay: Record<string, { scans: number; vulnerabilities: number }> = {}
    
    scanActivity.forEach(scan => {
      const day = scan.createdAt.toISOString().split('T')[0]
      if (!activityByDay[day]) {
        activityByDay[day] = { scans: 0, vulnerabilities: 0 }
      }
      activityByDay[day].scans += 1
      activityByDay[day].vulnerabilities += scan.totalVulns
    })

    // Convert to array format for charts
    const activityData = Object.entries(activityByDay).map(([date, data]) => ({
      date,
      scans: data.scans,
      vulnerabilities: data.vulnerabilities
    }))

    return NextResponse.json({
      overview: {
        totalAssets,
        totalScans,
        totalVulnerabilities,
        activeScans: scanStatusCount.RUNNING + scanStatusCount.PENDING,
        completedScans: scanStatusCount.COMPLETED,
        failedScans: scanStatusCount.FAILED,
        criticalVulns: vulnerabilitySeverityCount.CRITICAL,
        highVulns: vulnerabilitySeverityCount.HIGH,
        mediumVulns: vulnerabilitySeverityCount.MEDIUM,
        lowVulns: vulnerabilitySeverityCount.LOW
      },
      vulnerabilitySeverityCount,
      scanStatusCount,
      recentScans,
      topVulnerableAssets,
      activityData
    })

  } catch (error) {
    return handleApiError(error)
  }
}
