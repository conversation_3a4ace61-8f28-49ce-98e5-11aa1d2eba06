import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError, NotFoundError, AuthorizationError, ValidationError } from '@/lib/errors'
import { processManager } from '@/lib/process-manager'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Await params
    const { id } = await params

    // Check if scan exists and belongs to user
    const scan = await db.scan.findUnique({
      where: { id },
      select: { 
        id: true, 
        userId: true, 
        status: true, 
        targetUrl: true,
        startedAt: true 
      }
    })

    if (!scan) {
      throw new NotFoundError('Scan not found')
    }

    if (scan.userId !== currentUser.userId) {
      throw new AuthorizationError('Access denied')
    }

    // Check if scan can be cancelled
    if (scan.status !== 'RUNNING' && scan.status !== 'PENDING') {
      throw new ValidationError(`Cannot cancel scan with status: ${scan.status}`)
    }

    // Try to kill the running process if it exists
    let processKilled = false
    if (scan.status === 'RUNNING') {
      processKilled = processManager.killProcess(id)
      
      if (processKilled) {
        console.log(`Successfully killed process for scan ${id}`)
      } else {
        console.log(`No running process found for scan ${id}, updating status anyway`)
      }
    }

    // Update scan status to CANCELLED
    const updatedScan = await db.scan.update({
      where: { id },
      data: {
        status: 'CANCELLED',
        completedAt: new Date(),
        duration: scan.startedAt 
          ? Math.round((Date.now() - new Date(scan.startedAt).getTime()) / 1000)
          : 0,
        errorMessage: 'Scan cancelled by user',
        updatedAt: new Date()
      },
      select: {
        id: true,
        targetUrl: true,
        status: true,
        startedAt: true,
        completedAt: true,
        duration: true,
        errorMessage: true
      }
    })

    return NextResponse.json({
      message: 'Scan cancelled successfully',
      scan: updatedScan,
      processKilled
    })

  } catch (error) {
    return handleApiError(error)
  }
}
