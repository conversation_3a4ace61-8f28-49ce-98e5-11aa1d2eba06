import React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface PageHeaderProps {
  title: string
  description?: string
  backButton?: {
    href: string
    label?: string
  }
  actions?: React.ReactNode
  className?: string
  variant?: 'default' | 'centered'
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  backButton,
  actions,
  className,
  variant = 'default',
}) => {
  if (variant === 'centered') {
    return (
      <div className={cn('text-center mb-8', className)}>
        {backButton && (
          <div className="mb-6">
            <Link href={backButton.href}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                {backButton.label || 'Back'}
              </Button>
            </Link>
          </div>
        )}
        <h1 className="text-3xl font-light text-gray-900 mb-3">{title}</h1>
        {description && (
          <p className="text-lg font-light text-gray-500 max-w-2xl mx-auto">
            {description}
          </p>
        )}
        {actions && <div className="mt-6">{actions}</div>}
      </div>
    )
  }

  return (
    <div className={cn('mb-8', className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {backButton && (
            <Link href={backButton.href}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                {backButton.label || 'Back'}
              </Button>
            </Link>
          )}
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">{title}</h1>
            {description && (
              <p className="text-gray-500 mt-1">{description}</p>
            )}
          </div>
        </div>
        {actions && <div className="flex items-center space-x-3">{actions}</div>}
      </div>
    </div>
  )
}
