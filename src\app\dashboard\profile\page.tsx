'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { updateProfileSchema, type UpdateProfileInput } from '@/lib/validations'
import { useAuth } from '@/contexts/auth-context'
import { <PERSON><PERSON><PERSON>r, PageHeader } from '@/components/layout'
import { Card, CardContent, CardHeader, CardTitle, Button, Input, Label, Alert, Badge } from '@/components/ui'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import {
  User,
  Save,
  Calendar,
  Mail,
  Building,
  Globe,
  Edit3,
  Check,
  Shield,
  Activity,
  Clock,
  MapPin,
  Phone,
  Briefcase,
  Settings,
  Download,
  Upload,
  Camera,
  Star,
  Award
} from 'lucide-react'

export default function ProfilePage() {
  const { user, refreshUser } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useForm<UpdateProfileInput>({
    resolver: zodResolver(updateProfileSchema),
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      companyName: user?.companyName || '',
      country: user?.country || '',
    },
  })

  const onSubmit = async (data: UpdateProfileInput) => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update profile')
      }

      setSuccess('Profile updated successfully!')
      await refreshUser()
      reset(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Something went wrong')
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = () => {
    reset({
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      companyName: user?.companyName || '',
      country: user?.country || '',
    })
    setError(null)
    setSuccess(null)
  }

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <PageContainer maxWidth="full" className="space-y-6">
        <PageHeader
          title="User Profile"
          description="Manage your personal information and account preferences"
          actions={
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <Upload className="h-4 w-4 mr-2" />
                Import Settings
              </Button>
            </div>
          }
        />

        {/* Enhanced Profile Header */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardContent className="p-8">
            <div className="flex items-center space-x-6">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center text-white font-bold text-2xl shadow-lg">
                  {getInitials(user.firstName, user.lastName)}
                </div>
                <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-white rounded-full shadow-lg border-2 border-gray-100 flex items-center justify-center hover:bg-gray-50 transition-colors">
                  <Camera className="h-4 w-4 text-gray-600" />
                </button>
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h1 className="text-2xl font-bold text-gray-900">
                    {user.firstName} {user.lastName}
                  </h1>
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <Shield className="h-3 w-3 mr-1" />
                    Verified
                  </Badge>
                </div>
                <p className="text-gray-600 mb-3">{user.email}</p>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    Joined {new Date(user.createdAt).toLocaleDateString()}
                  </div>
                  <div className="flex items-center">
                    <Activity className="h-4 w-4 mr-1" />
                    Last updated {new Date(user.updatedAt).toLocaleDateString()}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-600">98%</div>
                <div className="text-sm text-gray-500">Profile Complete</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Alert Messages */}
        {error && (
          <div className="mb-6">
            <Alert variant="error" className="border-0 shadow-lg bg-red-50/80 backdrop-blur-sm">
              {error}
            </Alert>
          </div>
        )}

        {success && (
          <div className="mb-6">
            <Alert variant="success" className="border-0 shadow-lg bg-green-50/80 backdrop-blur-sm">
              {success}
            </Alert>
          </div>
        )}

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Main Profile Form */}
          <div className="xl:col-span-2">
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                  <User className="h-5 w-5 mr-2 text-blue-500" />
                  Personal Information
                </CardTitle>
              </CardHeader>

              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="firstName" className="text-sm font-medium text-gray-700">
                        First Name
                      </Label>
                      <Input
                        id="firstName"
                        type="text"
                        autoComplete="given-name"
                        {...register('firstName')}
                        className={`bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500 ${
                          errors.firstName ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                        }`}
                        disabled={isLoading}
                        placeholder="Enter your first name"
                      />
                      {errors.firstName && (
                        <p className="text-sm text-red-600 flex items-center mt-1">
                          <span className="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                          {errors.firstName.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="lastName" className="text-sm font-medium text-gray-700">
                        Last Name
                      </Label>
                      <Input
                        id="lastName"
                        type="text"
                        autoComplete="family-name"
                        {...register('lastName')}
                        className={`bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500 ${
                          errors.lastName ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                        }`}
                        disabled={isLoading}
                        placeholder="Enter your last name"
                      />
                      {errors.lastName && (
                        <p className="text-sm text-red-600 flex items-center mt-1">
                          <span className="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                          {errors.lastName.message}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="companyName" className="text-sm font-medium text-gray-700">
                        Company Name
                      </Label>
                      <div className="relative">
                        <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          id="companyName"
                          type="text"
                          autoComplete="organization"
                          {...register('companyName')}
                          className={`pl-10 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500 ${
                            errors.companyName ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                          }`}
                          disabled={isLoading}
                          placeholder="Enter your company name"
                        />
                      </div>
                      {errors.companyName && (
                        <p className="text-sm text-red-600 flex items-center mt-1">
                          <span className="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                          {errors.companyName.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="country" className="text-sm font-medium text-gray-700">
                        Country
                      </Label>
                      <div className="relative">
                        <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          id="country"
                          type="text"
                          autoComplete="country-name"
                          {...register('country')}
                          className={`pl-10 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500 ${
                            errors.country ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                          }`}
                          disabled={isLoading}
                          placeholder="Enter your country"
                        />
                      </div>
                      {errors.country && (
                        <p className="text-sm text-red-600 flex items-center mt-1">
                          <span className="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                          {errors.country.message}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end pt-6 border-t border-gray-200">
                    <div className="flex space-x-3">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => reset()}
                        disabled={isLoading}
                        className="border-gray-200 hover:bg-gray-50"
                      >
                        Reset
                      </Button>
                      <Button
                        type="submit"
                        disabled={isLoading || !isDirty}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        {isLoading ? (
                          <>
                            <LoadingSpinner size="sm" className="mr-2" />
                            Updating...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Account Summary Sidebar */}
          <div className="xl:col-span-1 space-y-6">
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                  <Shield className="h-5 w-5 mr-2 text-green-500" />
                  Account Summary
                </CardTitle>
              </CardHeader>

              <CardContent className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Mail className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">Email Address</p>
                    <p className="text-sm text-gray-600 truncate">{user.email}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <Building className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">Company</p>
                    <p className="text-sm text-gray-600 truncate">{user.companyName || 'Not specified'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Globe className="h-4 w-4 text-purple-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">Country</p>
                    <p className="text-sm text-gray-600 truncate">{user.country || 'Not specified'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                    <Calendar className="h-4 w-4 text-orange-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">Member Since</p>
                    <p className="text-sm text-gray-600">{new Date(user.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium text-green-700">Active Account</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Account Statistics */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                  <Activity className="h-5 w-5 mr-2 text-blue-500" />
                  Account Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-xl font-bold text-blue-600">12</div>
                    <div className="text-xs text-gray-600">Total Scans</div>
                  </div>
                  <div className="text-center p-3 bg-red-50 rounded-lg">
                    <div className="text-xl font-bold text-red-600">3</div>
                    <div className="text-xs text-gray-600">Critical Issues</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-xl font-bold text-green-600">5</div>
                    <div className="text-xs text-gray-600">Assets</div>
                  </div>
                  <div className="text-center p-3 bg-purple-50 rounded-lg">
                    <div className="text-xl font-bold text-purple-600">98%</div>
                    <div className="text-xs text-gray-600">Security Score</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageContainer>
    </div>
  )
}
