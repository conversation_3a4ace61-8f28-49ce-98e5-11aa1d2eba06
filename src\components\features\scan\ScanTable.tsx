import { <PERSON><PERSON>, <PERSON><PERSON>, In<PERSON>, <PERSON>, Card<PERSON>eader, Card<PERSON><PERSON>le, CardContent, Alert } from '@/components/ui'
import { DataTable } from '@/components/layout/data-table'
import { ScanStatusBadge } from '@/components/ui/status-badge'
import { NoSSR } from '@/components/no-ssr'
import Link from 'next/link'
import { Eye, ExternalLink, Square, AlertTriangle } from 'lucide-react'

export function ScanTable({
  scans,
  loading,
  onCancel,
  cancellingScans = new Set(),
}: {
  scans: any[]
  loading: boolean
  onCancel: (scanId: string) => void
  cancellingScans?: Set<string>
}) {
  const columns = [
    {
      key: 'target',
      header: 'Target',
      className: 'w-1/3',
      render: (scan: any) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className={`w-3 h-3 rounded-full ${
              scan.status === 'COMPLETED' ? 'bg-green-400' :
              scan.status === 'RUNNING' ? 'bg-blue-400 animate-pulse' :
              scan.status === 'FAILED' ? 'bg-red-400' :
              scan.status === 'PENDING' ? 'bg-yellow-400' :
              'bg-gray-400'
            }`} />
          </div>
          <div className="min-w-0 flex-1">
            <div className="text-sm font-medium text-gray-900 truncate">
              {scan.asset?.title || scan.targetUrl}
            </div>
            <div className="text-xs text-gray-500 truncate">
              {scan.asset?.domain || new URL(scan.targetUrl).hostname}
            </div>
            {scan.duration && (
              <div className="text-xs text-gray-400 mt-1">
                Duration: {formatDuration(scan.duration)}
              </div>
            )}
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      className: 'w-24',
      render: (scan: any) => (
        <div className="space-y-1">
          <ScanStatusBadge status={scan.status} size="sm" />
          {scan.startedAt && (
            <div className="text-xs text-gray-500">
              <NoSSR fallback="...">
                {new Date(scan.startedAt).toLocaleDateString()}
              </NoSSR>
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'vulnerabilities',
      header: 'Vulnerabilities',
      className: 'w-1/4',
      render: (scan: any) => (
        <div className="space-y-2">
          <div className="flex items-center space-x-1">
            <span className="text-lg font-bold text-gray-900">{scan.totalVulns}</span>
            <span className="text-xs text-gray-500">total</span>
          </div>
          <div className="flex flex-wrap gap-1">
            {scan.criticalVulns > 0 && (
              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 text-xs px-1 py-0">
                {scan.criticalVulns} Critical
              </Badge>
            )}
            {scan.highVulns > 0 && (
              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 text-xs px-1 py-0">
                {scan.highVulns} High
              </Badge>
            )}
            {scan.mediumVulns > 0 && (
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 text-xs px-1 py-0">
                {scan.mediumVulns} Medium
              </Badge>
            )}
            {scan.lowVulns > 0 && (
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs px-1 py-0">
                {scan.lowVulns} Low
              </Badge>
            )}
            {scan.infoVulns > 0 && (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 text-xs px-1 py-0">
                {scan.infoVulns} Info
              </Badge>
            )}
          </div>
        </div>
      ),
    },
    {
      key: 'created',
      header: 'Created',
      className: 'w-32',
      render: (scan: any) => (
        <div className="text-sm">
          <div className="text-gray-900">
            <NoSSR fallback="Loading...">
              {new Date(scan.createdAt).toLocaleDateString()}
            </NoSSR>
          </div>
          <div className="text-xs text-gray-500">
            <NoSSR fallback="...">
              {new Date(scan.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </NoSSR>
          </div>
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      className: 'w-40',
      render: (scan: any) => (
        <div className="flex items-center space-x-1">
          <Link href={`/dashboard/scans/${scan.id}`}>
            <Button size="sm" variant="outline" className="text-xs px-2 py-1">
              <Eye className="h-3 w-3 mr-1" />
              View
            </Button>
          </Link>
          {scan.asset && (
            <Link href={`/dashboard/assets/${scan.asset.id}`}>
              <Button size="sm" variant="outline" className="text-xs px-2 py-1">
                <ExternalLink className="h-3 w-3 mr-1" />
                Asset
              </Button>
            </Link>
          )}
          {(scan.status === 'RUNNING' || scan.status === 'PENDING') && (
            <Button
              size="sm"
              variant="destructive"
              onClick={() => onCancel(scan.id)}
              disabled={cancellingScans.has(scan.id)}
              className="bg-red-600 hover:bg-red-700 text-xs px-2 py-1"
            >
              {cancellingScans.has(scan.id) ? (
                <AlertTriangle className="h-3 w-3 animate-spin" />
              ) : (
                <Square className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      ),
    },
  ]

  function formatDuration(seconds?: number) {
    if (!seconds) return 'N/A'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  return (
    <DataTable
      data={scans}
      columns={columns}
      loading={loading}
      emptyState={{
        title: 'No scans found',
        description: 'Try adjusting your filters to see more results.',
        action: undefined
      }}
    />
  )
}
