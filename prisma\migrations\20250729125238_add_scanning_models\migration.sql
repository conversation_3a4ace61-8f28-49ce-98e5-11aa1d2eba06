-- CreateTable
CREATE TABLE `assets` (
    `id` VARCHAR(191) NOT NULL,
    `url` VARCHAR(500) NOT NULL,
    `domain` VARCHAR(255) NOT NULL,
    `title` VARCHAR(500) NULL,
    `description` TEXT NULL,
    `technology` JSON NULL,
    `status` ENUM('ACTIVE', 'INACTIVE', 'ARCHIVED') NOT NULL DEFAULT 'ACTIVE',
    `last_scanned` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,

    INDEX `assets_user_id_idx`(`user_id`),
    INDEX `assets_domain_idx`(`domain`),
    INDEX `assets_status_idx`(`status`),
    UNIQUE INDEX `assets_user_id_url_key`(`user_id`, `url`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `scans` (
    `id` VARCHAR(191) NOT NULL,
    `target_url` VARCHAR(500) NOT NULL,
    `status` ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'PENDING',
    `started_at` DATETIME(3) NULL,
    `completed_at` DATETIME(3) NULL,
    `duration` INTEGER NULL,
    `total_vulns` INTEGER NOT NULL DEFAULT 0,
    `critical_vulns` INTEGER NOT NULL DEFAULT 0,
    `high_vulns` INTEGER NOT NULL DEFAULT 0,
    `medium_vulns` INTEGER NOT NULL DEFAULT 0,
    `low_vulns` INTEGER NOT NULL DEFAULT 0,
    `info_vulns` INTEGER NOT NULL DEFAULT 0,
    `error_message` TEXT NULL,
    `nuclei_version` VARCHAR(50) NULL,
    `template_count` INTEGER NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `asset_id` VARCHAR(191) NULL,

    INDEX `scans_user_id_idx`(`user_id`),
    INDEX `scans_status_idx`(`status`),
    INDEX `scans_started_at_idx`(`started_at`),
    INDEX `scans_asset_id_idx`(`asset_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `vulnerabilities` (
    `id` VARCHAR(191) NOT NULL,
    `template_id` VARCHAR(255) NOT NULL,
    `name` VARCHAR(500) NOT NULL,
    `severity` ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO', 'UNKNOWN') NOT NULL,
    `description` TEXT NULL,
    `reference` JSON NULL,
    `tags` JSON NULL,
    `matcher` TEXT NULL,
    `extracted_results` JSON NULL,
    `request` TEXT NULL,
    `response` TEXT NULL,
    `curl_command` TEXT NULL,
    `host` VARCHAR(500) NOT NULL,
    `matched_at` VARCHAR(500) NOT NULL,
    `timestamp` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `scan_id` VARCHAR(191) NOT NULL,
    `asset_id` VARCHAR(191) NULL,

    INDEX `vulnerabilities_scan_id_idx`(`scan_id`),
    INDEX `vulnerabilities_asset_id_idx`(`asset_id`),
    INDEX `vulnerabilities_severity_idx`(`severity`),
    INDEX `vulnerabilities_template_id_idx`(`template_id`),
    INDEX `vulnerabilities_host_idx`(`host`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `assets` ADD CONSTRAINT `assets_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `scans` ADD CONSTRAINT `scans_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `scans` ADD CONSTRAINT `scans_asset_id_fkey` FOREIGN KEY (`asset_id`) REFERENCES `assets`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `vulnerabilities` ADD CONSTRAINT `vulnerabilities_scan_id_fkey` FOREIGN KEY (`scan_id`) REFERENCES `scans`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `vulnerabilities` ADD CONSTRAINT `vulnerabilities_asset_id_fkey` FOREIGN KEY (`asset_id`) REFERENCES `assets`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
