import { ChildProcess } from 'child_process'

interface RunningProcess {
  scanId: string
  process: ChildProcess
  startTime: number
  targetUrl: string
}

class ProcessManager {
  private runningProcesses = new Map<string, RunningProcess>()

  /**
   * Register a running process for a scan
   */
  registerProcess(scanId: string, process: ChildProcess, targetUrl: string): void {
    console.log(`Registering process for scan ${scanId}`)
    
    this.runningProcesses.set(scanId, {
      scanId,
      process,
      startTime: Date.now(),
      targetUrl
    })

    // Auto-cleanup when process exits
    process.on('exit', () => {
      this.unregisterProcess(scanId)
    })

    process.on('error', () => {
      this.unregisterProcess(scanId)
    })
  }

  /**
   * Unregister a process (called automatically when process exits)
   */
  unregisterProcess(scanId: string): void {
    if (this.runningProcesses.has(scanId)) {
      console.log(`Unregistering process for scan ${scanId}`)
      this.runningProcesses.delete(scanId)
    }
  }

  /**
   * Kill a running process for a specific scan
   */
  killProcess(scanId: string): boolean {
    const runningProcess = this.runningProcesses.get(scanId)
    
    if (!runningProcess) {
      console.log(`No running process found for scan ${scanId}`)
      return false
    }

    try {
      console.log(`Killing process for scan ${scanId} (PID: ${runningProcess.process.pid})`)
      
      // Try graceful termination first
      runningProcess.process.kill('SIGTERM')
      
      // Force kill after 5 seconds if still running
      setTimeout(() => {
        if (!runningProcess.process.killed) {
          console.log(`Force killing process for scan ${scanId}`)
          runningProcess.process.kill('SIGKILL')
        }
      }, 5000)

      this.unregisterProcess(scanId)
      return true
    } catch (error) {
      console.error(`Failed to kill process for scan ${scanId}:`, error)
      return false
    }
  }

  /**
   * Check if a scan has a running process
   */
  hasRunningProcess(scanId: string): boolean {
    return this.runningProcesses.has(scanId)
  }

  /**
   * Get information about a running process
   */
  getProcessInfo(scanId: string): RunningProcess | null {
    return this.runningProcesses.get(scanId) || null
  }

  /**
   * Get all running processes
   */
  getAllRunningProcesses(): RunningProcess[] {
    return Array.from(this.runningProcesses.values())
  }

  /**
   * Kill all running processes (for cleanup)
   */
  killAllProcesses(): void {
    console.log(`Killing ${this.runningProcesses.size} running processes`)
    
    for (const [scanId, runningProcess] of this.runningProcesses) {
      try {
        runningProcess.process.kill('SIGTERM')
      } catch (error) {
        console.error(`Failed to kill process for scan ${scanId}:`, error)
      }
    }

    this.runningProcesses.clear()
  }

  /**
   * Get statistics about running processes
   */
  getStats(): {
    totalRunning: number
    processes: Array<{
      scanId: string
      targetUrl: string
      duration: number
      pid?: number
    }>
  } {
    const now = Date.now()
    const processes = Array.from(this.runningProcesses.values()).map(p => ({
      scanId: p.scanId,
      targetUrl: p.targetUrl,
      duration: Math.round((now - p.startTime) / 1000),
      pid: p.process.pid
    }))

    return {
      totalRunning: this.runningProcesses.size,
      processes
    }
  }
}

// Singleton instance
export const processManager = new ProcessManager()

// Graceful shutdown - kill all processes when the application exits
process.on('SIGTERM', () => {
  console.log('Shutting down process manager...')
  processManager.killAllProcesses()
})

process.on('SIGINT', () => {
  console.log('Shutting down process manager...')
  processManager.killAllProcesses()
})

process.on('exit', () => {
  processManager.killAllProcesses()
})
