import React from 'react'
import { cn } from '@/lib/utils'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  color?: 'primary' | 'secondary' | 'white'
}

interface LoadingStateProps {
  children?: React.ReactNode
  className?: string
}

const sizeConfig = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12',
}

const colorConfig = {
  primary: 'border-blue-600',
  secondary: 'border-gray-600',
  white: 'border-white',
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className,
  color = 'primary',
}) => {
  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2 border-t-transparent',
        sizeConfig[size],
        colorConfig[color],
        className
      )}
    />
  )
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  children,
  className,
}) => {
  return (
    <div className={cn('flex items-center justify-center min-h-32', className)}>
      <div className="text-center">
        <LoadingSpinner size="lg" className="mx-auto mb-4" />
        {children && (
          <p className="text-sm text-gray-500">{children}</p>
        )}
      </div>
    </div>
  )
}

export const FullPageLoading: React.FC<LoadingStateProps> = ({
  children,
  className,
}) => {
  return (
    <div className={cn('flex items-center justify-center min-h-screen', className)}>
      <div className="text-center">
        <LoadingSpinner size="xl" className="mx-auto mb-4" />
        {children && (
          <p className="text-lg text-gray-500">{children}</p>
        )}
      </div>
    </div>
  )
}
