const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs').promises

async function checkNucleiInstallation() {
  console.log('🔍 Diagnosing Nuclei Installation...\n')

  // 1. Check if nuclei command is available
  console.log('1. Checking if nuclei command is available...')
  try {
    const result = await runCommand('nuclei', ['--version'])
    if (result.success) {
      console.log('✅ Nuclei is installed and accessible')
      console.log(`   Version output: ${result.stdout.trim()}`)
    } else {
      console.log('❌ Nuclei command not found or failed')
      console.log(`   Error: ${result.stderr}`)
      return false
    }
  } catch (error) {
    console.log('❌ Error checking nuclei installation:', error.message)
    return false
  }

  // 2. Check nuclei help
  console.log('\n2. Checking nuclei help command...')
  try {
    const result = await runCommand('nuclei', ['--help'])
    if (result.success) {
      console.log('✅ Nuclei help command works')
    } else {
      console.log('❌ Nuclei help command failed')
      console.log(`   Error: ${result.stderr}`)
    }
  } catch (error) {
    console.log('❌ Error checking nuclei help:', error.message)
  }

  // 3. Check nuclei templates
  console.log('\n3. Checking nuclei templates...')
  try {
    const result = await runCommand('nuclei', ['-list-templates'])
    if (result.success) {
      const templateCount = result.stdout.split('\n').filter(line => line.trim()).length
      console.log(`✅ Nuclei templates are available (${templateCount} templates found)`)
    } else {
      console.log('❌ Nuclei templates check failed')
      console.log(`   Error: ${result.stderr}`)
      console.log('   Trying to update templates...')
      
      const updateResult = await runCommand('nuclei', ['-update-templates'])
      if (updateResult.success) {
        console.log('✅ Templates updated successfully')
      } else {
        console.log('❌ Template update failed')
        console.log(`   Error: ${updateResult.stderr}`)
      }
    }
  } catch (error) {
    console.log('❌ Error checking nuclei templates:', error.message)
  }

  // 4. Test a simple scan
  console.log('\n4. Testing a simple nuclei scan...')
  try {
    const tempDir = path.join(process.cwd(), 'temp', 'nuclei')
    await fs.mkdir(tempDir, { recursive: true })
    
    const outputFile = path.join(tempDir, 'test-scan.json')
    const args = [
      '-target', 'https://httpbin.org',
      '-jsonl',
      '-output', outputFile,
      '-silent',
      '-no-color',
      '-severity', 'info',
      '-timeout', '10'
    ]

    console.log(`   Running: nuclei ${args.join(' ')}`)
    const result = await runCommand('nuclei', args, 30000) // 30 second timeout

    console.log(`   Exit code: ${result.code}`)
    console.log(`   Stdout: ${result.stdout}`)
    console.log(`   Stderr: ${result.stderr}`)

    if (result.success || result.code === 1) { // Code 1 is OK (vulnerabilities found)
      console.log('✅ Test scan completed successfully')
      
      // Check if output file was created
      try {
        const outputExists = await fs.access(outputFile).then(() => true).catch(() => false)
        if (outputExists) {
          const content = await fs.readFile(outputFile, 'utf8')
          console.log(`   Output file created with ${content.split('\n').filter(l => l.trim()).length} lines`)
        } else {
          console.log('   No output file created (this might be normal if no vulnerabilities found)')
        }
      } catch (error) {
        console.log('   Error checking output file:', error.message)
      }
    } else {
      console.log('❌ Test scan failed')
      console.log(`   This is the same error you're experiencing (exit code ${result.code})`)
    }
  } catch (error) {
    console.log('❌ Error running test scan:', error.message)
  }

  // 5. Check environment
  console.log('\n5. Checking environment...')
  console.log(`   OS: ${process.platform}`)
  console.log(`   Node.js version: ${process.version}`)
  console.log(`   Working directory: ${process.cwd()}`)
  console.log(`   PATH: ${process.env.PATH}`)

  return true
}

function runCommand(command, args, timeout = 10000) {
  return new Promise((resolve) => {
    const child = spawn(command, args, { stdio: 'pipe' })
    
    let stdout = ''
    let stderr = ''
    let timedOut = false

    const timer = setTimeout(() => {
      timedOut = true
      child.kill('SIGTERM')
    }, timeout)

    child.stdout?.on('data', (data) => {
      stdout += data.toString()
    })

    child.stderr?.on('data', (data) => {
      stderr += data.toString()
    })

    child.on('close', (code) => {
      clearTimeout(timer)
      resolve({
        success: !timedOut && (code === 0 || code === 1),
        code,
        stdout,
        stderr,
        timedOut
      })
    })

    child.on('error', (error) => {
      clearTimeout(timer)
      resolve({
        success: false,
        code: -1,
        stdout,
        stderr: error.message,
        timedOut
      })
    })
  })
}

// Run the diagnostic
if (require.main === module) {
  checkNucleiInstallation().then(() => {
    console.log('\n🏁 Diagnostic complete!')
    console.log('\nIf you see any ❌ errors above, those need to be fixed for scanning to work.')
    console.log('\nCommon solutions:')
    console.log('1. Reinstall Nuclei: go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest')
    console.log('2. Update templates: nuclei -update-templates')
    console.log('3. Check if Go is properly installed and in PATH')
    console.log('4. Restart your terminal/IDE after installing')
  }).catch(error => {
    console.error('Diagnostic failed:', error)
  })
}

module.exports = { checkNucleiInstallation }
