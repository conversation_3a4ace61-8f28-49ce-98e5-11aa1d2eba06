import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError, NotFoundError } from '@/lib/errors'

export async function DELETE(request: NextRequest) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Check if user exists
    const user = await db.user.findUnique({
      where: { id: currentUser.userId },
      select: { id: true }
    })

    if (!user) {
      throw new NotFoundError('User not found')
    }

    // Delete user and all associated data using transaction
    await db.$transaction(async (tx) => {
      // Delete vulnerabilities first (they reference scans)
      await tx.vulnerability.deleteMany({
        where: {
          scan: {
            userId: currentUser.userId
          }
        }
      })

      // Delete scans (they reference assets and users)
      await tx.scan.deleteMany({
        where: { userId: currentUser.userId }
      })

      // Delete assets
      await tx.asset.deleteMany({
        where: { userId: currentUser.userId }
      })

      // Finally delete the user
      await tx.user.delete({
        where: { id: currentUser.userId }
      })
    })

    // Clear the auth cookie by setting it to expire
    const response = NextResponse.json(
      {
        message: 'Account deleted successfully',
      },
      { status: 200 }
    )

    // Clear auth cookie
    response.cookies.set('auth-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0, // Expire immediately
      path: '/',
    })

    return response

  } catch (error) {
    return handleApiError(error)
  }
}
