const { nucleiScanner } = require('../src/lib/nuclei')

async function testScan() {
  console.log('🔍 Testing Nuclei Scanner Integration...\n')

  try {
    // Check if Nuclei is installed
    console.log('1. Checking Nuclei installation...')
    const isInstalled = await nucleiScanner.checkNucleiInstallation()
    
    if (!isInstalled) {
      console.error('❌ Nuclei is not installed or not accessible')
      console.log('Please install Nuclei first:')
      console.log('  go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest')
      process.exit(1)
    }
    
    console.log('✅ Nuclei is installed and accessible')

    // Test scan on a safe target
    console.log('\n2. Running test scan on httpbin.org...')
    const testUrl = 'https://httpbin.org'
    
    const scanResult = await nucleiScanner.scan({
      target: testUrl,
      severity: ['info', 'low'],
      timeout: 10,
      concurrency: 5,
      rateLimit: 50
    })

    if (scanResult.success) {
      console.log('✅ <PERSON><PERSON> completed successfully!')
      console.log(`📊 Results:`)
      console.log(`   - Total vulnerabilities: ${scanResult.stats.totalVulnerabilities}`)
      console.log(`   - Duration: ${scanResult.stats.duration} seconds`)
      console.log(`   - Templates used: ${scanResult.stats.totalTemplates}`)
      
      if (scanResult.stats.nucleiVersion) {
        console.log(`   - Nuclei version: ${scanResult.stats.nucleiVersion}`)
      }

      console.log('\n📋 Severity breakdown:')
      Object.entries(scanResult.stats.severityCount).forEach(([severity, count]) => {
        if (count > 0) {
          console.log(`   - ${severity}: ${count}`)
        }
      })

      if (scanResult.vulnerabilities.length > 0) {
        console.log('\n🔍 Sample vulnerabilities:')
        scanResult.vulnerabilities.slice(0, 3).forEach((vuln, index) => {
          console.log(`   ${index + 1}. ${vuln.info.name} (${vuln.info.severity})`)
          console.log(`      Template: ${vuln['template-id']}`)
          console.log(`      Host: ${vuln.host}`)
        })
      }
    } else {
      console.error('❌ Scan failed:', scanResult.error)
      process.exit(1)
    }

    console.log('\n✅ All tests passed! The scanning system is working correctly.')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    process.exit(1)
  }
}

// Run the test
if (require.main === module) {
  testScan()
}

module.exports = { testScan }
