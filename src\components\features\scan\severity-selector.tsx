import React from 'react'
import { cn } from '@/lib/utils'
import { Label } from '@/components/ui/label'

interface SeveritySelectorProps {
  value: string[]
  onChange: (value: string[]) => void
  disabled?: boolean
  className?: string
  label?: string
}

const severityOptions = [
  { value: 'critical', label: 'Critical', color: 'bg-red-500', textColor: 'text-red-700', borderColor: 'border-red-500' },
  { value: 'high', label: 'High', color: 'bg-orange-500', textColor: 'text-orange-700', borderColor: 'border-orange-500' },
  { value: 'medium', label: 'Medium', color: 'bg-yellow-500', textColor: 'text-yellow-700', borderColor: 'border-yellow-500' },
  { value: 'low', label: 'Low', color: 'bg-blue-500', textColor: 'text-blue-700', borderColor: 'border-blue-500' },
  { value: 'info', label: 'Info', color: 'bg-green-500', textColor: 'text-green-700', borderColor: 'border-green-500' },
  { value: 'unknown', label: 'Unknown', color: 'bg-gray-500', textColor: 'text-gray-700', borderColor: 'border-gray-500' },
]

export const SeveritySelector: React.FC<SeveritySelectorProps> = ({
  value,
  onChange,
  disabled = false,
  className,
  label = 'Scan Severity',
}) => {
  const handleChange = (severityValue: string, checked: boolean) => {
    if (checked) {
      onChange([...value, severityValue])
    } else {
      onChange(value.filter(v => v !== severityValue))
    }
  }

  return (
    <div className={cn('space-y-4', className)}>
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3">
        {severityOptions.map((severity) => {
          const isChecked = value.includes(severity.value)

          return (
            <label
              key={severity.value}
              className={cn(
                'relative flex flex-col items-center p-4 rounded-xl border-2 cursor-pointer transition-all duration-200',
                'hover:shadow-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2',
                {
                  'cursor-not-allowed opacity-50': disabled,
                  'hover:border-gray-300 hover:shadow-md': !disabled && !isChecked,
                  [`${severity.borderColor} bg-white shadow-md transform scale-105`]: isChecked,
                  'border-gray-200 bg-white': !isChecked,
                }
              )}
            >
              <input
                type="checkbox"
                checked={isChecked}
                onChange={(e) => handleChange(severity.value, e.target.checked)}
                className="sr-only"
                disabled={disabled}
              />
              <div
                className={cn(
                  'w-4 h-4 rounded-full border-2 transition-all mb-3',
                  {
                    [`${severity.color} border-transparent`]: isChecked,
                    'border-gray-300 bg-white': !isChecked,
                  }
                )}
              />
              <span
                className={cn(
                  'text-sm font-semibold transition-colors text-center',
                  {
                    [severity.textColor]: isChecked,
                    'text-gray-600': !isChecked,
                  }
                )}
              >
                {severity.label}
              </span>
              {isChecked && (
                <div className={cn(
                  'absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white',
                  severity.color
                )} />
              )}
            </label>
          )
        })}
      </div>

      <div className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3 text-center">
        <span className="font-semibold text-gray-900">{value.length}</span> of {severityOptions.length} severity levels selected
      </div>
    </div>
  )
}
