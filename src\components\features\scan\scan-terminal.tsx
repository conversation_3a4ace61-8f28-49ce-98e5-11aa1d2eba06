import React, { useEffect, useRef, useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Terminal, 
  Copy, 
  Download, 
  Maximize2, 
  Minimize2,
  Trash2,
  Play,
  Pause
} from 'lucide-react'
import { type ScanLog } from '@/hooks/use-scan-events'

interface ScanTerminalProps {
  logs: ScanLog[]
  isConnected: boolean
  className?: string
}

export const ScanTerminal: React.FC<ScanTerminalProps> = ({
  logs,
  isConnected,
  className
}) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [autoScroll, setAutoScroll] = useState(true)
  const terminalRef = useRef<HTMLDivElement>(null)
  const endRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (autoScroll && !isPaused && endRef.current) {
      endRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [logs, autoScroll, isPaused])

  const handleCopyLogs = async () => {
    const logText = logs
      .map(log => {
        const timestamp = log.timestamp instanceof Date
          ? log.timestamp.toLocaleTimeString()
          : new Date(log.timestamp).toLocaleTimeString()
        return `[${timestamp}] [${log.stream}] ${log.message}`
      })
      .join('\n')

    try {
      await navigator.clipboard.writeText(logText)
    } catch (err) {
      console.error('Failed to copy logs:', err)
    }
  }

  const handleDownloadLogs = () => {
    const logText = logs
      .map(log => {
        const timestamp = log.timestamp instanceof Date
          ? log.timestamp.toLocaleTimeString()
          : new Date(log.timestamp).toLocaleTimeString()
        return `[${timestamp}] [${log.stream}] ${log.message}`
      })
      .join('\n')

    const blob = new Blob([logText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `nuclei-scan-logs-${new Date().toISOString().slice(0, 19)}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleClearLogs = () => {
    // Note: This would need to be implemented to clear logs from parent state
    console.log('Clear logs requested')
  }

  const formatLogMessage = (log: ScanLog) => {
    // Handle timestamp whether it's a Date object or string
    const timestamp = log.timestamp instanceof Date
      ? log.timestamp.toLocaleTimeString()
      : new Date(log.timestamp).toLocaleTimeString()
    const streamColor = log.stream === 'stderr' ? 'text-yellow-400' : 'text-green-400'

    return (
      <div key={`${timestamp}-${log.stream}-${Math.random()}`} className="font-mono text-sm leading-relaxed">
        <span className="text-gray-500">[{timestamp}]</span>
        <span className={`ml-2 ${streamColor}`}>[{log.stream}]</span>
        <span className="ml-2 text-gray-300">{log.message}</span>
      </div>
    )
  }

  return (
    <Card className={`${className} bg-gray-900 border-gray-700 shadow-xl`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Terminal className="h-5 w-5 text-green-400" />
            <CardTitle className="text-white text-lg">Security Assessment Console</CardTitle>
            <div className="flex items-center space-x-2">
              <Badge
                variant={isConnected ? "default" : "secondary"}
                className={isConnected ? "bg-green-600 text-white" : "bg-gray-600 text-gray-300"}
              >
                {isConnected ? "Active" : "Inactive"}
              </Badge>
              <Badge variant="outline" className="text-gray-300 border-gray-600">
                {logs.length} events
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsPaused(!isPaused)}
              className="text-gray-400 hover:text-white hover:bg-gray-800"
              title={isPaused ? "Resume auto-scroll" : "Pause auto-scroll"}
            >
              {isPaused ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopyLogs}
              className="text-gray-400 hover:text-white hover:bg-gray-800"
              title="Copy logs to clipboard"
            >
              <Copy className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDownloadLogs}
              className="text-gray-400 hover:text-white hover:bg-gray-800"
              title="Download logs"
            >
              <Download className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-400 hover:text-white hover:bg-gray-800"
              title={isExpanded ? "Minimize" : "Maximize"}
            >
              {isExpanded ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div 
          ref={terminalRef}
          className={`bg-black p-4 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 ${
            isExpanded ? 'h-96' : 'h-48'
          }`}
        >
          {logs.length === 0 ? (
            <div className="text-gray-500 text-center py-8">
              <Terminal className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>Initializing security assessment...</p>
              <p className="text-sm mt-1">Real-time progress will appear here</p>
            </div>
          ) : (
            <div className="space-y-1">
              {logs.map((log) => formatLogMessage(log))}
              <div ref={endRef} />
            </div>
          )}
        </div>
        
        {/* Terminal footer */}
        <div className="bg-gray-800 px-4 py-2 border-t border-gray-700">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <div className="flex items-center space-x-4">
              <span>Auto-scroll: {autoScroll ? 'ON' : 'OFF'}</span>
              <span>Status: {isPaused ? 'PAUSED' : 'ACTIVE'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <label className="flex items-center space-x-1">
                <input
                  type="checkbox"
                  checked={autoScroll}
                  onChange={(e) => setAutoScroll(e.target.checked)}
                  className="w-3 h-3"
                />
                <span>Auto-scroll</span>
              </label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
