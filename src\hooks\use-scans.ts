import { useState, useCallback } from 'react'
import { getScans, cancelScan } from '@/lib/api/scans'

export function useScans({ initialPage = 1, initialLimit = 10, initialStatus = '' } = {}) {
  const [scans, setScans] = useState([])
  const [pagination, setPagination] = useState({ page: initialPage, limit: initialLimit, total: 0, pages: 0 })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchScans = useCallback(async (page = pagination.page, status = initialStatus) => {
    setLoading(true)
    setError(null)
    try {
      const data = await getScans({ page, limit: pagination.limit, status })
      setScans(data.scans)
      setPagination(data.pagination)
    } catch (err: any) {
      setError(err.message || 'Failed to fetch scans')
    } finally {
      setLoading(false)
    }
  }, [pagination.limit, pagination.page, initialStatus])

  const handleCancelScan = useCallback(async (scanId: string) => {
    setError(null)
    try {
      await cancelScan(scanId)
      await fetchScans()
    } catch (err: any) {
      setError(err.message || 'Failed to cancel scan')
    }
  }, [fetchScans])

  return {
    scans,
    pagination,
    loading,
    error,
    fetchScans,
    handleCancelScan,
    setPagination,
    setScans,
    setError
  }
}
