import fs from 'fs'
import path from 'path'

const tempDir = path.join(__dirname, '../../temp/nuclei')

export async function cleanupTempFiles() {
  const files = await fs.promises.readdir(tempDir)
  for (const file of files) {
    const filePath = path.join(tempDir, file)
    try {
      await fs.promises.unlink(filePath)
    } catch (err) {
      // Optionally log error, ignore if file already deleted
    }
  }
}
